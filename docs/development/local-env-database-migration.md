# 本地开发环境数据库迁移指南

## 概述

由于生产环境已将数据库名从"逾期债权数据库"改为"overdue_debt_db"，本地开发环境也需要同步更新。

## 迁移步骤

### 1. 备份现有数据（如果有重要数据）

```bash
# 导出现有数据
mysqldump -u root -p"Zlb&198838" "逾期债权数据库" > backup_chinese_db_local.sql
```

### 2. 创建新数据库

```bash
# 登录MySQL
mysql -u root -p"Zlb&198838"

# 创建新数据库
CREATE DATABASE IF NOT EXISTS `overdue_debt_db` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

# 如果有备份数据，导入到新数据库
USE overdue_debt_db;
SOURCE backup_chinese_db_local.sql;
```

### 3. 更新本地配置文件

确保以下文件已更新（脚本已自动修改）：
- `api-gateway/src/main/resources/application.yml`
- `docker-compose.yml`
- `docker-compose.local.yml`

### 4. 验证配置

检查数据库连接URL是否正确：
```yaml
spring:
  datasource:
    url: ***********************************************************************************
```

### 5. 重启本地服务

```bash
# 如果使用Docker
docker-compose down
docker-compose up -d

# 如果直接运行
mvn spring-boot:run -pl api-gateway
```

### 6. 验证连接

```bash
# 测试API健康检查
curl http://localhost:8080/actuator/health

# 测试登录接口
curl -X POST http://localhost:8080/api/auth/login \
  -H 'Content-Type: application/json' \
  -d '{"username":"admin","password":"admin123"}'
```

## 注意事项

1. **表名保持不变**：只修改数据库名，所有表名（新增表、处置表等）保持中文不变
2. **字符编码**：确保使用utf8mb4字符集
3. **IDE配置**：更新IDE中的数据库连接配置
4. **环境变量**：如果使用环境变量配置，更新相应的.env文件

## 常见问题

### Q: 是否需要修改Java代码？
A: 不需要。所有数据库名引用都在配置文件中，代码中只有注释被更新。

### Q: 旧的中文数据库怎么处理？
A: 确认数据迁移成功后，可以删除旧数据库：
```sql
DROP DATABASE IF EXISTS `逾期债权数据库`;
```

### Q: 如何回滚？
A: 使用备份目录中的配置文件恢复：
```bash
# 查看备份
ls backups/config-*/

# 恢复配置（选择合适的备份目录）
cp backups/config-20250707-*/application.yml api-gateway/src/main/resources/
```

## 更新时间

- 2025-07-07：初始迁移，数据库名从"逾期债权数据库"改为"overdue_debt_db"