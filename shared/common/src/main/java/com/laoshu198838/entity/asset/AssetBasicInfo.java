package com.laoshu198838.entity.asset;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 资产基本信息实体类
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "asset_basic_info")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class AssetBasicInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "asset_name", nullable = false, length = 200)
    private String assetName;

    @Column(name = "property_owner_id", nullable = false)
    private Long propertyOwnerId;

    @Column(name = "management_company", nullable = false, length = 100)
    private String managementCompany;

    @Column(name = "property_certificate_no", length = 100)
    private String propertyCertificateNo;

    @Column(name = "has_property_certificate", columnDefinition = "TINYINT(1) DEFAULT 1")
    private Boolean hasPropertyCertificate = true;

    @Column(name = "acquisition_date")
    private LocalDate acquisitionDate;

    @Column(name = "purchase_contract_no", length = 200)
    private String purchaseContractNo;

    @Column(name = "purchase_price", precision = 20, scale = 2)
    private BigDecimal purchasePrice;

    @Column(name = "location", length = 500)
    private String location;

    @Column(name = "total_area", nullable = false, precision = 15, scale = 2)
    private BigDecimal totalArea;

    @Column(name = "property_years")
    private Integer propertyYears;

    @Column(name = "used_years")
    private Integer usedYears;

    @Column(name = "remaining_years")
    private Integer remainingYears;

    @Enumerated(EnumType.STRING)
    @Column(name = "asset_type", nullable = false)
    private AssetType assetType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private AssetStatus status = AssetStatus.ACTIVE;

    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    @PrePersist
    protected void onCreate() {
        createdTime = LocalDateTime.now();
        updatedTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }

    /**
     * 资产类型枚举
     */
    public enum AssetType {
        PROPERTY("房产"),
        LAND("土地");

        private final String description;

        AssetType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 资产状态枚举
     */
    public enum AssetStatus {
        ACTIVE("正常"),
        INACTIVE("停用"),
        DISPOSED("已处置");

        private final String description;

        AssetStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
