package com.laoshu198838.dto.asset;

import com.laoshu198838.entity.asset.AssetBasicInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 资产基本信息DTO
 * 
 * <AUTHOR>
 */
@Data
public class AssetBasicInfoDTO {

    private Long id;
    private String assetName;
    private Long propertyOwnerId;
    private String propertyOwnerName; // 关联查询获取
    private String managementCompany;
    private String propertyCertificateNo;
    private Boolean hasPropertyCertificate;
    private LocalDate acquisitionDate;
    private String purchaseContractNo;
    private BigDecimal purchasePrice;
    private String location;
    private BigDecimal totalArea;
    private Integer propertyYears;
    private Integer usedYears;
    private Integer remainingYears;
    private AssetBasicInfo.AssetType assetType;
    private String assetTypeDescription;
    private AssetBasicInfo.AssetStatus status;
    private String statusDescription;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String createdBy;
    private String updatedBy;

    // 扩展字段
    private BigDecimal currentSelfUseArea; // 当前自用面积
    private BigDecimal currentRentalArea; // 当前出租面积
    private BigDecimal currentIdleArea; // 当前闲置面积
    private BigDecimal activationRate; // 盘活率
    private Integer activeRentalCount; // 有效出租合同数量
    private BigDecimal totalMonthlyRent; // 总月租金

    /**
     * 从实体转换为DTO
     */
    public static AssetBasicInfoDTO fromEntity(AssetBasicInfo entity) {
        if (entity == null) {
            return null;
        }

        AssetBasicInfoDTO dto = new AssetBasicInfoDTO();
        dto.setId(entity.getId());
        dto.setAssetName(entity.getAssetName());
        dto.setPropertyOwnerId(entity.getPropertyOwnerId());
        dto.setManagementCompany(entity.getManagementCompany());
        dto.setPropertyCertificateNo(entity.getPropertyCertificateNo());
        dto.setHasPropertyCertificate(entity.getHasPropertyCertificate());
        dto.setAcquisitionDate(entity.getAcquisitionDate());
        dto.setPurchaseContractNo(entity.getPurchaseContractNo());
        dto.setPurchasePrice(entity.getPurchasePrice());
        dto.setLocation(entity.getLocation());
        dto.setTotalArea(entity.getTotalArea());
        dto.setPropertyYears(entity.getPropertyYears());
        dto.setUsedYears(entity.getUsedYears());
        dto.setRemainingYears(entity.getRemainingYears());
        dto.setAssetType(entity.getAssetType());
        dto.setAssetTypeDescription(entity.getAssetType() != null ? entity.getAssetType().getDescription() : null);
        dto.setStatus(entity.getStatus());
        dto.setStatusDescription(entity.getStatus() != null ? entity.getStatus().getDescription() : null);
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setUpdatedBy(entity.getUpdatedBy());

        return dto;
    }

    /**
     * 转换为实体
     */
    public AssetBasicInfo toEntity() {
        AssetBasicInfo entity = new AssetBasicInfo();
        entity.setId(this.id);
        entity.setAssetName(this.assetName);
        entity.setPropertyOwnerId(this.propertyOwnerId);
        entity.setManagementCompany(this.managementCompany);
        entity.setPropertyCertificateNo(this.propertyCertificateNo);
        entity.setHasPropertyCertificate(this.hasPropertyCertificate);
        entity.setAcquisitionDate(this.acquisitionDate);
        entity.setPurchaseContractNo(this.purchaseContractNo);
        entity.setPurchasePrice(this.purchasePrice);
        entity.setLocation(this.location);
        entity.setTotalArea(this.totalArea);
        entity.setPropertyYears(this.propertyYears);
        entity.setUsedYears(this.usedYears);
        entity.setRemainingYears(this.remainingYears);
        entity.setAssetType(this.assetType);
        entity.setStatus(this.status);
        entity.setCreatedBy(this.createdBy);
        entity.setUpdatedBy(this.updatedBy);

        return entity;
    }
}
