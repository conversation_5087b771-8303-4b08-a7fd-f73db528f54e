package com.laoshu198838.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据源切换注解
 * 用于标识方法或类使用的数据源
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataSource {
    
    /**
     * 数据源标识
     * primary: 主数据源（overdue_debt_db）
     * secondary: 第二数据源（金蝶数据库）
     * userSystem: 用户系统数据源（user_system数据库）
     * asset-management: 资产管理数据源（asset_management数据库）
     */
    String value() default "primary";
}
