**目的**: 项目清理和维护

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

清理 $ARGUMENTS 中的项目文件、依赖和构建产物。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/cleanup --code --dry-run` - 预览代码清理变更
- `/cleanup --deps --all` - 删除未使用的依赖
- `/cleanup --files --watch` - 持续文件清理

清理模式:

**--code:** 删除未使用的导入和死代码 | 清理console.log和调试代码 | 删除注释块 | 修复风格不一致 | 删除超过30天的TODO

**--files:** 删除构建产物和临时文件 | 清理损坏的node_modules | 删除日志和缓存目录 | 清理测试输出 | 删除操作系统文件 (.DS_Store, thumbs.db)

**--deps:** 从package.json删除未使用的依赖 | 更新有漏洞的依赖 | 清理重复依赖 | 优化依赖树 | 检查过时的包

**--git:** 删除未跟踪的文件 (需确认) | 清理已合并的分支 | 从历史中删除大文件/不需要的文件 | 优化git (.git/objects清理) | 清理无效引用

**--cfg:** 删除已弃用的配置设置 | 清理未使用的环境变量 | 更新过时的配置格式 | 验证配置一致性 | 删除重复条目

**--all:** 全面清理所有区域 | 生成详细报告 | 建议维护计划 | 提供性能影响分析

**--dry-run:** 显示将要清理的内容而不做更改 | 估算空间节省和性能影响 | 清理前识别风险

**--watch:** 监控并自动清理新产生的构建产物 | 开发期间持续清理 | 防止临时文件累积 | 实时维护

## 集成和最佳实践

@include shared/research-patterns.yml#Mandatory_Research_Flows

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates