**目的**: 技术文档编写和知识传递

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

为 $ARGUMENTS 中指定的概念、代码或系统提供全面的解释。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/explain --depth beginner --style tutorial "React hooks"` - 为初学者解释React hooks
- `/explain --depth advanced --visual "B-tree索引"` - 深入讲解数据库索引
- `/explain --depth expert --think "快速排序优化"` - 算法解释带复杂度分析

解释模式:

**--depth:** 解释深度级别
- beginner: 基础概念，简单语言 | intermediate: 标准技术深度
- advanced: 深入技术细节 | expert: 前沿技术与内部原理

**--style:** 解释风格
- tutorial: 逐步学习指导 | reference: 快速查找格式
- conversational: 自然对话式 | academic: 正式且详尽

**--visual:** 包含可视化辅助
- 图表和流程图 | 带注释的代码示例
- 架构可视化 | 流程序列图

@include shared/research-patterns.yml#Explanation_Methodology

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates