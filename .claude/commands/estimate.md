**目的**: 项目复杂度和时间估算

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

为 $ARGUMENTS 中指定的任务提供全面的时间、复杂度和资源估算。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/estimate "Add user authentication"` - 快速功能估算
- `/estimate --scope project --detail high --team medium` - 详细项目估算
- `/estimate --scope migration --team large --ultrathink` - 迁移项目估算

估算模式:

**--scope:** 估算范围
- feature: 单个功能估算 | epic: 多功能史诗
- project: 完整项目范围 | refactor: 代码重构工作量 | migration: 数据/系统迁移

**--team:** 团队规模
- solo: 单个开发者 | small: 2-3个开发者
- medium: 4-8个开发者 | large: 9+个开发者

**--detail:** 估算详细级别
- high: 详细分解 | medium: 标准估算 | low: 快速粗略估算

## 估算框架

@include shared/execution-patterns.yml#Estimation_Methodology

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates