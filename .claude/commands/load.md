**目的**: 项目上下文加载和分析

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

加载和分析 $ARGUMENTS 中的项目上下文。

## 目的
加载和分析 $ARGUMENTS 中的项目上下文，构建对代码库结构、架构和模式的全面理解。

## 语法
`/load [标志] [路径]`

@include shared/flag-inheritance.yml#Universal_Always

## 核心标志

--scope 标志:
- minimal: 仅核心文件
- standard: 主要源码和配置
- comprehensive: 所有相关文件
- full: 完整项目分析

--focus 标志:
- architecture: 系统设计
- api: API端点和契约
- database: 数据库模式和查询
- frontend: UI组件
- testing: 测试覆盖率

--format 标志:
- summary: 高级概述
- detailed: 综合分析
- visual: 包含图表
- structured: YAML/JSON输出

## 加载策略

渐进式加载:
1. 核心文件（package.json、配置）
2. 入口点（main、index）
3. 关键模块和组件
4. 测试和文档
5. 支持文件

智能选择:
- 按重要性排序
- 跳过生成的文件
- 专注于活跃代码
- 包含关键配置
- 遵循.gitignore

## 分析组件

结构分析:
- 目录组织
- 模块依赖
- 组件层次
- API表面积
- 数据库模式

模式检测:
- 使用的设计模式
- 编码约定
- 架构风格
- 技术栈
- 最佳实践

质量指标:
- 代码复杂度
- 测试覆盖率
- 文档水平
- 技术债务
- 安全问题

## 输出格式

标准报告:
```yaml
Project: [名称]
Type: [Web应用/API/库]
Stack:
  Frontend: [技术]
  Backend: [技术]
  Database: [类型]
Architecture:
  Style: [单体/微服务]
  Patterns: [列表]
Key_Components:
  - [组件]: [目的]
Quality:
  Test_Coverage: X%
  Documentation: [级别]
  Complexity: [分数]
```

## 最佳实践

效率:
- 增量加载
- 缓存分析结果
- 专注于变更
- 跳过冗余文件
- 优化内存使用

准确性:
- 验证假设
- 交叉引用文件
- 检查文档
- 验证模式
- 定期更新

## 示例

```bash
# 快速项目概述
/load --scope minimal

# 完整架构分析
/load --scope comprehensive --focus architecture

# API文档生成
/load --focus api --format detailed

# 完整项目理解
/load --scope full --think-hard
```

## 集成

@include shared/loading-config.yml#Loading_Strategies

配合使用:
- /analyze 用于深度检查
- /document 用于文档生成
- /improve 用于增强改进
- /estimate 用于规划估算

## 交付物

- 项目结构图
- 架构图
- 组件清单
- 依赖关系图
- 质量指标报告
- 模式分析