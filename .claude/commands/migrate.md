**目的**: 数据库和代码迁移管理

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

为 $ARGUMENTS 执行数据库和代码迁移。

## 目的
基于 $ARGUMENTS 中的规范执行综合数据库或代码迁移，具有安全检查和回滚功能。

## 语法
`/migrate [标志] [迁移规范]`

@include shared/flag-inheritance.yml#Universal_Always

## 核心标志

--type 标志:
- database: SQL数据库模式迁移
- code: 代码库重构
- api: API版本迁移
- data: 数据转换
- full: 完整系统迁移

--direction 标志:
- up: 向前应用迁移
- down: 回滚迁移
- status: 检查迁移状态
- validate: 测试但不应用

--target 标志:
- latest: 所有待处理迁移
- version: 特定版本
- step: 迁移步骤数
- timestamp: 到指定日期/时间

## 迁移类型

数据库迁移:
- 数据库模式更改（添加/删除表）
- 列修改
- 索引优化
- 约束更新
- 数据转换

代码迁移:
- API版本更新
- 框架升级
- 依赖更新
- 结构重构
- 模式现代化

数据迁移:
- 格式转换
- 批量转换
- ETL操作
- 清理操作
- 规范化

## 安全特性

迁移前:
- 备份当前状态
- 验证迁移文件
- 检查依赖关系
- 在事务中测试
- 评估影响

迁移中:
- 事务包装
- 进度跟踪
- 错误处理
- 部分回滚
- 健康检查

迁移后:
- 验证完整性
- 更新文档
- 清理缓存
- 运行测试
- 监控性能

## 迁移工作流

1. 分析阶段:
   - 扫描当前状态
   - 识别需要的更改
   - 生成迁移计划
   - 评估风险

2. 准备阶段:
   - 创建备份
   - 准备回滚计划
   - 设置监控
   - 通知相关人员

3. 执行阶段:
   - 运行迁移
   - 跟踪进度
   - 处理错误
   - 应用修复

4. 验证阶段:
   - 验证成功
   - 运行测试套件
   - 检查性能
   - 更新文档

## 最佳实践

安全性:
- 始终先备份
- 在测试环境测试
- 使用事务
- 规划回滚
- 密切监控

性能:
- 批量操作
- 非峰值时间执行
- 索引管理
- 查询优化
- 资源限制

## 示例

```bash
# 数据库迁移到最新版本
/migrate --type database --direction up

# 回滚最近2个迁移
/migrate --type database --direction down --step 2

# 验证代码迁移
/migrate --type code --validate --think

# 完整系统迁移计划
/migrate --type full --plan --ultrathink
```

## 交付物

- 迁移脚本
- 回滚程序
- 执行日志
- 验证报告
- 更新文档
- 性能指标