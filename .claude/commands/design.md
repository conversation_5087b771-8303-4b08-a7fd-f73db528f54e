**目的**: 系统架构和API设计

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

为 $ARGUMENTS 设计系统架构和API。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/design --api --think` - 带模式的REST API设计
- `/design --ddd --think-hard` - 深度领域建模
- `/design --api --ddd --ultrathink` - 完整系统架构

设计模式:

**--api:** 设计REST或GraphQL API
- 配合 --openapi: 生成OpenAPI 3.0规范 | 配合 --graphql: 创建GraphQL模式和解析器
- 包含认证、限流和错误处理 | 面向可扩展性和可维护性设计

**--ddd:** 应用DDD原则
- 配合 --bounded-context: 定义上下文边界和映射
- 设计实体、值对象和聚合 | 创建领域服务和事件 | 实现存储库模式

**--prd:** 创建PRD
- 配合 --template: 使用模板（功能/api/集成/迁移）
- 包含用户故事和验收标准 | 定义成功指标和时间线 | 记录技术要求

## 设计模式

@include shared/architecture-patterns.yml#API_Design_Patterns

@include shared/architecture-patterns.yml#DDD_Patterns

@include shared/architecture-patterns.yml#PRD_Templates

## 集成和最佳实践

组合模式: API+DDD: 设计领域驱动的API | API+PRD: 创建API产品需求 | DDD+PRD: 记录领域驱动架构 | 三者结合: 完整系统设计

最佳实践: 从用户需求和业务目标开始 | 面向变化和演进的设计 | 早期考虑非功能性需求 | 记录决策和理由 | 包含示例和图表 | 规划测试和监控

@include shared/research-patterns.yml#Mandatory_Research_Flows

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates