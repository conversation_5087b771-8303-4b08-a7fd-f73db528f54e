# SuperClaude 命令参考手册 v2.0.1

## 目录
- [快速开始](#快速开始)
- [通用标志（所有命令可用）](#通用标志所有命令可用)
- [认知角色标志](#认知角色标志)
- [完整命令参考](#完整命令参考)
- [标志组合与最佳实践](#标志组合与最佳实践)

---

## 快速开始

**基本用法**: `/command [标志] [参数]`

**示例命令**:
```bash
/review --files src/ --quality --evidence    # 带证据的综合代码审查
/analyze --code --persona-architect          # 以架构师角度进行代码分析
/build --react --magic --tdd                # 构建带AI组件的React应用
/troubleshoot --prod --five-whys --seq      # 生产环境调试与推理
/task:create "添加用户认证"                   # 创建和管理复杂功能
/deploy --env prod --plan --validate        # 安全的生产环境部署
```

---

## 通用标志（所有命令可用）

### 🧠 思维深度控制
| 标志 | 描述 | Token使用量 |
|------|-------------|-------------|
| `--think` | 多文件分析，扩展上下文 | ~4K tokens |
| `--think-hard` | 架构级深度分析 | ~10K tokens |
| `--ultrathink` | 关键系统分析，最大深度 | ~32K tokens |

### 📦 Token优化
| 标志 | 别名 | 描述 |
|------|-------|-------------|
| `--uc` | `--ultracompressed` | 启用超压缩模式（大幅减少token使用） |

### 🔧 MCP服务器控制
| 标志 | 描述 |
|------|-------------|
| `--c7` | 启用Context7文档查找 |
| `--seq` | 启用Sequential思维分析 |
| `--magic` | 启用Magic UI组件生成 |
| `--pup` | 启用Puppeteer浏览器自动化 |
| `--all-mcp` | 启用所有MCP服务器，获得最大能力 |
| `--no-mcp` | 禁用所有MCP服务器（仅使用原生工具） |
| `--no-c7` | 特定禁用Context7 |
| `--no-seq` | 特定禁用Sequential思维 |
| `--no-magic` | 特定禁用Magic UI构建器 |
| `--no-pup` | 特定禁用Puppeteer |

### 🔍 分析与内省
| 标志 | 描述 |
|------|-------------|
| `--introspect` | 启用自我感知分析，提供认知透明度 |

### 📋 规划与执行
| 标志 | 描述 |
|------|-------------|
| `--plan` | 运行前显示详细执行计划 |
| `--dry-run` | 预览更改，不执行实际操作 |
| `--watch` | 持续监控，实时反馈 |
| `--interactive` | 逐步引导过程 |
| `--force` | 覆盖安全检查（谨慎使用） |

### ✅ 质量与验证
| 标志 | 描述 |
|------|-------------|
| `--validate` | 增强的执行前安全检查 |
| `--security` | 以安全为重点的分析和验证 |
| `--coverage` | 生成综合覆盖率分析 |
| `--strict` | 零容忍模式，增强验证 |

---

## 认知角色标志

所有认知角色现在都集成为标志，每个命令都可使用：

| 角色标志 | 专业领域 | 最适合 |
|--------------|-----------|----------|
| `--persona-architect` | 系统思维、可扩展性、模式 | 架构决策、系统设计 |
| `--persona-frontend` | UI/UX专注、无障碍优先 | 用户界面、组件设计 |
| `--persona-backend` | API、数据库、可靠性 | 服务器架构、数据建模 |
| `--persona-analyzer` | 根因分析、循证方法 | 复杂调试、问题调查 |
| `--persona-security` | 威胁建模、零信任、OWASP | 安全审计、漏洞评估 |
| `--persona-mentor` | 教学、引导学习、清晰度 | 文档编写、知识传递 |
| `--persona-refactorer` | 代码质量、可维护性 | 代码清理、技术债务 |
| `--persona-performance` | 优化、性能分析、效率 | 性能调优、瓶颈分析 |
| `--persona-qa` | 测试、边界情况、验证 | 质量保证、测试覆盖 |

---

## 完整命令参考

### 🛠️ 开发命令 (3个)

#### `/build` - 通用项目构建器
使用现代技术栈模板构建项目、功能和组件。

**命令特定标志:**
- `--init` - 初始化新项目并设置技术栈
- `--feature` - 使用现有模式实现功能
- `--tdd` - 测试驱动开发工作流
- `--react` - React + Vite + TypeScript + Router
- `--api` - Express.js API + TypeScript
- `--fullstack` - 完整的React + Node.js + Docker
- `--mobile` - React Native + Expo
- `--cli` - Commander.js CLI + 测试

**示例:**
```bash
/build --init --react --magic --tdd         # 新React应用+AI组件
/build --feature "认证系统" --tdd            # 带测试的功能
/build --api --openapi --seq                # 带文档的API
```

#### `/dev-setup` - 开发环境配置
配置专业开发环境，包含CI/CD和监控。

**命令特定标志:**
- `--install` - 安装和配置依赖
- `--ci` - CI/CD流水线配置
- `--monitor` - 监控和可观测性设置
- `--docker` - 容器化设置
- `--testing` - 测试基础设施
- `--team` - 团队协作工具
- `--standards` - 代码质量标准

**示例:**
```bash
/dev-setup --install --ci --monitor         # 完整环境配置
/dev-setup --team --standards --docs        # 团队设置
```

#### `/test` - 综合测试框架
创建、运行和维护跨技术栈的测试策略。

**命令特定标志:**
- `--e2e` - 端到端测试
- `--integration` - 集成测试
- `--unit` - 单元测试
- `--visual` - 视觉回归测试
- `--mutation` - 变异测试
- `--performance` - 性能测试
- `--accessibility` - 无障碍测试
- `--parallel` - 并行测试执行

**示例:**
```bash
/test --coverage --e2e --pup               # 完整测试套件
/test --mutation --strict                  # 测试质量验证
```

### 🔍 分析与改进命令 (5个)

#### `/review` - AI代码审查
基于证据的综合代码审查和质量分析。

**命令特定标志:**
- `--files` - 审查指定文件或目录
- `--commit` - 审查指定提交的更改（HEAD、哈希、范围）
- `--pr` - 审查拉取请求更改（git diff main..branch）
- `--quality` - 专注代码质量问题（DRY、SOLID、复杂度）
- `--evidence` - 为所有建议提供来源和文档
- `--fix` - 为发现的问题建议具体修复方案
- `--summary` - 生成审查结果的执行摘要

**示例:**
```bash
/review --files src/auth.ts --persona-security    # 安全导向的文件审查
/review --commit HEAD --quality --evidence        # 带来源的质量审查
/review --pr 123 --all --interactive             # 综合PR审查
/review --files src/ --persona-performance --think # 性能分析
```

#### `/analyze` - 多维度分析
代码、架构、性能和安全的综合分析。

**命令特定标志:**
- `--code` - 代码质量分析
- `--architecture` - 系统设计评估
- `--profile` - 性能分析
- `--deps` - 依赖分析
- `--surface` - 快速概览
- `--deep` - 综合深度分析
- `--forensic` - 详细调查

**示例:**
```bash
/analyze --code --architecture --seq       # 全面分析
/analyze --profile --deep --persona-performance  # 性能深度分析
```

#### `/troubleshoot` - 专业调试
系统化调试和问题解决。

**命令特定标志:**
- `--investigate` - 系统性问题分析
- `--five-whys` - 根因分析
- `--prod` - 生产环境调试
- `--perf` - 性能问题调查
- `--fix` - 完整解决方案
- `--hotfix` - 紧急修复
- `--rollback` - 安全回滚

**示例:**
```bash
/troubleshoot --prod --five-whys --seq    # 生产环境根因分析
/troubleshoot --perf --fix --pup          # 性能问题修复
```

#### `/improve` - 增强与优化
基于证据的改进，可衡量结果。

**命令特定标志:**
- `--quality` - 代码结构改进
- `--performance` - 性能优化
- `--accessibility` - 无障碍改进
- `--iterate` - 迭代改进
- `--threshold` - 质量目标百分比
- `--refactor` - 系统化重构
- `--modernize` - 技术更新

**示例:**
```bash
/improve --quality --iterate --threshold 95%    # 质量改进
/improve --performance --cache --pup            # 性能提升
```

#### `/explain` - 技术文档
生成综合解释和文档。

**命令特定标志:**
- `--depth` - 复杂度级别（ELI5|beginner|intermediate|expert）
- `--visual` - 包含图表
- `--examples` - 代码示例
- `--api` - API文档
- `--architecture` - 系统文档
- `--tutorial` - 学习教程
- `--reference` - 参考文档

**示例:**
```bash
/explain --depth expert --visual --seq     # 专家级文档
/explain --api --examples --c7             # 带示例的API文档
```

### ⚙️ 运维命令 (6个)

#### `/deploy` - 应用部署
带回滚能力的安全部署。

**命令特定标志:**
- `--env` - 目标环境（dev|staging|prod）
- `--canary` - 金丝雀部署
- `--blue-green` - 蓝绿部署
- `--rolling` - 滚动部署
- `--checkpoint` - 创建检查点
- `--rollback` - 回滚到之前版本
- `--monitor` - 部署后监控

**示例:**
```bash
/deploy --env prod --canary --monitor      # 金丝雀生产部署
/deploy --rollback --env prod              # 紧急回滚
```

#### `/migrate` - 数据库与代码迁移
带回滚能力的安全迁移。

**命令特定标志:**
- `--database` - 数据库迁移
- `--code` - 代码迁移
- `--config` - 配置迁移
- `--dependencies` - 依赖更新
- `--backup` - 先创建备份
- `--rollback` - 回滚迁移
- `--validate` - 数据完整性检查

**示例:**
```bash
/migrate --database --backup --validate    # 安全数据库迁移
/migrate --code --dry-run                  # 预览代码更改
```

#### `/scan` - 安全与验证
综合安全审计和合规检查。

**命令特定标志:**
- `--owasp` - OWASP Top 10 合规
- `--secrets` - 密钥检测
- `--compliance` - 法规合规
- `--quality` - 代码质量验证
- `--automated` - 持续监控

**示例:**
```bash
/scan --security --owasp --deps           # 安全审计
/scan --compliance --gdpr --strict        # 合规检查
```

#### `/estimate` - 项目估算
带风险评估的专业估算。

**命令特定标志:**
- `--detailed` - 综合详细分解
- `--rough` - 快速估算
- `--worst-case` - 悲观估算
- `--agile` - 故事点估算
- `--complexity` - 技术评估
- `--resources` - 资源规划
- `--timeline` - 时间线规划
- `--risk` - 风险评估

**示例:**
```bash
/estimate --detailed --complexity --risk   # 全面估算
/estimate --agile --story-points          # 敏捷规划
```

#### `/cleanup` - 项目维护
带安全验证的专业清理。

**命令特定标志:**
- `--code` - 删除死代码
- `--files` - 清理构建产物
- `--deps` - 删除未使用的依赖
- `--git` - 清理Git仓库
- `--all` - 综合清理
- `--aggressive` - 深度清理
- `--conservative` - 安全清理

**示例:**
```bash
/cleanup --all --dry-run                  # 预览清理
/cleanup --code --deps --validate         # 代码清理
```

#### `/git` - Git工作流管理
带安全功能的专业Git操作。

**命令特定标志:**
- `--status` - 仓库状态
- `--commit` - 专业提交
- `--branch` - 分支管理
- `--sync` - 远程同步
- `--checkpoint` - 创建检查点
- `--merge` - 智能合并
- `--history` - 历史分析
- `--pre-commit` - 设置并运行pre-commit钩子

**示例:**
```bash
/git --checkpoint "重构前"                 # 安全检查点
/git --commit --validate --test          # 安全提交
/git --pre-commit                        # 设置pre-commit钩子
/git --commit --pre-commit               # 带验证的提交
```

### 🎨 设计与架构命令 (1个)

#### `/design` - 系统架构
带规范的专业系统设计。

**命令特定标志:**
- `--api` - REST/GraphQL设计
- `--ddd` - 领域驱动设计
- `--microservices` - 微服务架构
- `--event-driven` - 事件模式
- `--openapi` - OpenAPI规范
- `--graphql` - GraphQL架构
- `--bounded-context` - DDD上下文
- `--integration` - 集成模式

**示例:**
```bash
/design --api --ddd --openapi --seq      # 带DDD的API设计
/design --microservices --event-driven   # 微服务设计
```

### 🔄 工作流命令 (4个)

#### `/spawn` - 专门化智能体
生成专注的智能体处理并行任务。

**命令特定标志:**
- `--task` - 定义具体任务
- `--parallel` - 并发执行
- `--specialized` - 领域专业知识
- `--collaborative` - 多智能体协作
- `--sync` - 同步结果
- `--merge` - 合并输出

**示例:**
```bash
/spawn --task "前端测试" --parallel         # 并行测试
/spawn --collaborative --sync              # 团队模拟
```

#### `/document` - 文档创建
多格式的专业文档。

**命令特定标志:**
- `--user` - 用户指南
- `--technical` - 开发者文档
- `--markdown` - Markdown格式
- `--interactive` - 交互式文档
- `--multilingual` - 多语言
- `--maintain` - 维护计划

**示例:**
```bash
/document --api --interactive --examples   # API文档
/document --user --visual --multilingual   # 用户指南
```

#### `/load` - 项目上下文加载
加载和分析项目上下文。

**命令特定标志:**
- `--depth` - 分析深度（shallow|normal|deep）
- `--context` - 上下文保存
- `--patterns` - 模式识别
- `--relationships` - 依赖映射
- `--structure` - 项目结构
- `--health` - 项目健康度
- `--standards` - 编码标准

**示例:**
```bash
/load --depth deep --patterns --seq       # 深度分析
/load --structure --health --standards   # 项目评估
```

#### `/task` - 任务管理
跨会话的复杂功能管理，自动分解和恢复。

**命令特定操作:**
- `/task:create [描述]` - 创建新任务，自动分解
- `/task:status [任务ID]` - 检查任务状态和进度
- `/task:resume [任务ID]` - 中断后恢复工作
- `/task:update [任务ID] [更新]` - 更新任务进度和需求
- `/task:complete [任务ID]` - 标记任务完成并生成摘要

**核心功能:**
- **智能分解**: 自动复杂度分析和子任务创建
- **上下文保存**: 跨会话保存工作状态
- **进度跟踪**: 自动更新和阻塞检测
- **会话恢复**: 从检查点恢复，保持完整上下文

**示例:**
```bash
/task:create "实现OAuth 2.0认证系统"          # 创建复杂功能
/task:status oauth-task-id                   # 检查进度
/task:resume oauth-task-id                   # 中断后恢复
/task:update oauth-task-id "发现库冲突"       # 更新发现的问题
/task:complete oauth-task-id                 # 完成并生成摘要
```

---

## 标志组合与最佳实践

### 🚀 专业工作流

**全栈开发**
```bash
/design --api --ddd --persona-architect
/build --fullstack --tdd --magic
/test --coverage --e2e --pup
/deploy --env staging --validate
```

**安全优先开发**
```bash
/scan --security --owasp --deps --persona-security
/analyze --security --forensic --seq
/improve --security --validate --strict
/test --security --coverage
```

**性能优化**
```bash
/analyze --profile --deep --persona-performance
/troubleshoot --perf --investigate --pup
/improve --performance --iterate --threshold 90%
/test --performance --load
```

**质量保证**
```bash
/review --quality --evidence --persona-qa
/improve --quality --refactor --strict
/scan --validate --quality
/test --coverage --mutation
```

### 💡 最佳实践

1. **始终验证风险操作**
   ```bash
   /deploy --env prod --validate --plan
   /migrate --database --dry-run --backup
   ```

2. **使用角色获得专业知识**
   ```bash
   /analyze --architecture --persona-architect
   /scan --security --persona-security
   ```

3. **结合MCP服务器获得最大能力**
   ```bash
   /build --react --magic --seq --c7
   /test --e2e --pup --coverage
   ```

4. **复杂任务的渐进式思考**
   ```bash
   /troubleshoot --investigate --think
   /design --microservices --think-hard
   /analyze --architecture --ultrathink
   ```

### 🎯 快速参考

**高风险操作**: 始终使用 `--validate` 或 `--dry-run`
**文档任务**: 启用 `--c7` 进行库查找
**复杂分析**: 使用 `--seq` 进行推理
**UI开发**: 启用 `--magic` 获得AI组件
**测试**: 使用 `--pup` 进行浏览器自动化
**节省Token**: 添加 `--uc` 减少70%使用量

---

**SuperClaude v2.0.1** - 19个专业命令 | 9个认知角色 | 高级MCP集成 | 循证方法论