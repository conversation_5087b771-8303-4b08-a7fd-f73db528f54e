**目的**: 基于证据的增强和优化

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

使用最佳实践和优化技术系统地改进 $ARGUMENTS 中的代码质量、性能和架构。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/improve --quality` - 代码质量改进
- `/improve --perf --iterate` - 性能优化
- `/improve --arch --think-hard` - 架构重构

## 命令特定标志
--quality: "代码质量改进（可读性、可维护性、DRY原则）"
--perf: "性能优化（算法、缓存、查询）"
--arch: "架构改进（模式、耦合、可扩展性）"
--refactor: "保持行为的安全重构"
--iterate: "迭代改进直到达到阈值"
--threshold: "质量阈值（low|medium|high|perfect）"
--metrics: "显示前后对比指标"
--safe: "保守模式 - 只进行安全更改"

## 改进类别

**代码质量:** 命名清晰度 | 函数提取 | 重复代码消除 | 复杂度降低 | 错误处理 | 类型安全

**性能:** 算法优化 | 查询优化 | 缓存策略 | 延迟加载 | 内存效率 | 并行处理

**架构:** 设计模式 | 依赖注入 | 层次分离 | 模块边界 | API设计 | 可扩展性模式

**可维护性:** 文档 | 测试覆盖率 | 配置提取 | 魔数消除 | 死代码清理

## 改进流程

**1. 分析:** 当前状态评估 | 识别改进领域 | 按影响力排序 | 设定可测量目标

**2. 规划:** 安全重构路径 | 保持功能性 | 增量更改 | 回滚策略

**3. 实施:** 小的原子更改 | 持续测试 | 性能监控 | 代码审查就绪

**4. 验证:** 行为保持 | 性能提升 | 质量指标 | 回归测试

@include shared/quality-patterns.yml#Code_Quality_Metrics

## 交付物

**改进代码:** 重构文件 | 保持功能 | 增强质量 | 更好性能

**改进报告:** 前后对比指标 | 更改摘要 | 性能提升 | 质量改进

**文档:** 重构决策 | 架构更改 | 性能优化 | 未来建议

@include shared/universal-constants.yml#Standard_Messages_Templates