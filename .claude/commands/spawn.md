**目的**: 并行专业化智能体

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

生成专业化子智能体来执行具有并行执行能力的专注任务。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/spawn --agent researcher "OAuth 2.0 最佳实践"` - 研究后实施
- `/spawn --mode parallel --agent builder "用户认证, 配置文件API"` - 并行开发
- `/spawn --mode sequential "研究 → 构建 → 审查支付"` - 完整周期工作流
- `/spawn --mode collaborative --ultrathink "设计微服务"` - 协作设计

## 智能体类型

研究智能体:
- 深入研究主题
- 比较解决方案
- 分析权衡
- 寻找最佳实践
- 记录发现

构建智能体:
- 生成代码
- 实现功能
- 创建测试
- 构建原型
- 集成系统

审查智能体:
- 代码质量检查
- 安全分析
- 性能审查
- 最佳实践验证
- 建议改进

优化智能体:
- 性能分析
- 资源优化
- 算法改进
- 数据库调优
- 缓存策略

文档智能体:
- API文档
- 用户指南
- 代码注释
- 架构文档
- README文件

## 执行模式

顺序模式:
```yaml
流程: 智能体1 → 智能体2 → 智能体3
用途: 当任务相互依赖时
示例: 研究 → 构建 → 审查
```

并行模式:
```yaml
流程: 智能体1 | 智能体2 | 智能体3
用途: 用于独立任务
示例: 多个功能构建
```

协作模式:
```yaml
流程: 智能体协同工作
用途: 复杂问题
示例: 系统设计会议
```

## 最佳实践

任务定义:
- 明确目标
- 具体交付物
- 成功标准
- 资源限制
- 时间约束

智能体选择:
- 匹配专业知识与任务
- 考虑依赖关系
- 规划协调
- 设定边界
- 定义交接

协调:
- 清晰沟通
- 共享上下文
- 进度跟踪
- 结果整合
- 质量控制

## 示例

```bash
# 先研究后实施
/spawn --agent researcher "OAuth 2.0 最佳实践"
/spawn --agent builder "基于研究实施 OAuth"

# 并行功能开发
/spawn --mode parallel --agent builder "用户认证, 配置文件API, 设置UI"

# 完整周期带审查
/spawn --mode sequential "研究 → 构建 → 审查支付集成"

# 协作系统设计
/spawn --mode collaborative --ultrathink "设计微服务架构"
```

## 集成

配合使用:
- 所有命令标志都会传递
- 继承角色偏好
- 共享项目上下文
- 累积发现
- 协调输出

## 交付物

- 智能体执行日志
- 任务完成报告
- 整合结果
- 性能指标
- 学习总结
- 交接文档

@include shared/universal-constants.yml#Standard_Messages_Templates