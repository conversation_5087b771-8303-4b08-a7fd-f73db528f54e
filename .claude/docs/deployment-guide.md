# Deployment & CI/CD Reference

## 🚀 Production Environment
- **Server**: Linux production server (**********)
- **Path**: `/opt/FinancialSystem/current`
- **Ports**: Frontend (80), Backend (8080), Database (3306)
- **Backup**: 30-day retention policy

## 🔄 CI/CD Pipeline Features
- **Automated deployment** triggered by Git pushes to main branch
- **Intelligent rollback** system with deployment history tracking
- **Comprehensive health checks** (API, database, frontend)
- **Auto-recovery mechanisms** for common issues
- **Docker image optimization** with local caching
- **Multi-environment support** (dev/prod configurations)

## 🐳 Docker Services
- **MySQL 8.0**: Database with health checks
- **Backend**: Spring Boot application (OpenJDK 21)
- **Frontend**: React application (Node.js 18)
- **Nginx**: Web server and reverse proxy
- **All Docker images are built and used locally to avoid pulling from remote registries, improving deployment speed and stability.**

## 🔧 Key Configuration Files
- **Backend Config**: `api-gateway/src/main/resources/application.yml`
- **Frontend Config**: `FinancialSystem-web/package.json`
- **Docker Config**: `docker-compose.yml`, `docker-compose.local.yml`
- **CI/CD Config**: `.ci-cd-config`, `ci-cd/` directory
- **Database Config**: Multi-datasource configuration in Spring Boot
- **Nginx Config**: `config/nginx/nginx.conf`

## 🖥️ Environment Notes
- 在本地是直接启动，不用使用docker启动，在linux中使用docker启动
- 记住我的linux系统中的是使用docker运行代码，docker镜像使用的是本地的镜像