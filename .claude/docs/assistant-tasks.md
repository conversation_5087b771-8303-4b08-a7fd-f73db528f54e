# Assistant Tasks & Guidelines

## 🤖 Assistant Tasks
- 每次我提交代码之后，我要帮你检查是否要更新相关文件
- **Code Analysis**: Always check for security vulnerabilities and performance issues
- **Documentation**: Update relevant documentation after code changes
- **Testing**: Verify functionality across all modules after updates

## 🔍 Problem-Solving Guidelines
- 关于重构的相关内容，都需要进行深入全面的分析
- 如果一个问题解决超过3次未解决，需要使用最强模型进行深入全面分析
- 如果问题再次出现或存在类似语气的情况，需要调用最强模型进行深入分析
- **Troubleshooting**: Check `/docs/troubleshooting/` for common issues and solutions
- **Performance**: Monitor health endpoints and review logs for performance issues
- **Security**: Always validate input and check for SQL injection, XSS vulnerabilities