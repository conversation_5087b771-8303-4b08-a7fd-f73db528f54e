# Database Schema Reference

## 💾 Primary Database (overdue_debt_db)
- **新增表 (OverdueDebtAdd)**: Main debt records with composite key
- **处置表 (OverdueDebtDecrease)**: Debt disposal/reduction records
- **减值准备表 (ImpairmentReserve)**: Impairment reserve data
- **诉讼表 (LitigationClaim)**: Litigation claims
- **非诉讼表 (NonLitigationClaim)**: Non-litigation claims

## 🔍 Metadata Details
- **数据库中存在中文数据库名,库名为overdue_debt_db** - Confirmed presence of Chinese database name for overdue debt database

## 👥 User System Database
- **User**: User authentication and profiles
- **Role**: Role definitions and permissions
- **Company**: Company structure and hierarchy
- **UserCompanyPermission**: User-company permission mapping

## 🏢 Kingdee Database
- Read-only integration with Kingdee ERP system
- Synchronized financial data for reporting