# Architecture Reference

## 🏗️ Core Technology Stack
- **Backend**: Spring Boot 3.1.12 with Java 21, JWT authentication, multi-datasource JPA
- **Frontend**: React 18.2.0 with Material-UI v5.15.20, Chart.js/Recharts for visualization
- **Databases**: Three MySQL 8.0 databases (overdue_debt_db, user_system, kingdee)
- **Build Tools**: <PERSON><PERSON> (multi-module) for backend, npm for frontend
- **Deployment**: Docker Compose with Nginx reverse proxy
- **CI/CD**: Automated deployment via webhook on main branch merge with rollback capability

## 📁 Module Structure
```
FinancialSystem/
├── api-gateway/              # Main API Gateway and Controllers
├── services/                 # Business Service Modules
│   ├── debt-management/      # Core debt management logic
│   ├── account-management/   # User and authentication services
│   ├── audit-management/     # Audit and logging services
│   └── report-management/    # Report generation services
├── shared/                   # Shared Components
│   ├── common/              # Entities, DTOs, utilities
│   ├── data-access/         # Repository layer and data config
│   ├── data-processing/     # Data processing services
│   └── report-core/         # Report generation core
├── integrations/            # External System Integrations
│   ├── treasury/            # Bank treasury system integration
│   ├── oa-workflow/         # OA workflow system integration
│   └── kingdee/             # Kingdee ERP integration
├── FinancialSystem-web/     # React Frontend Application
└── docs/                    # Comprehensive Documentation
```