# Claude Documentation Structure

This directory contains modular documentation files that are referenced from the main CLAUDE.md file to keep it concise while maintaining all necessary information.

## 📁 File Structure

- **`api-endpoints.md`** - Complete API endpoint documentation
- **`architecture.md`** - System architecture and technology stack details
- **`assistant-tasks.md`** - Guidelines for <PERSON> assistant tasks and problem-solving
- **`business-context.md`** - Business logic, entities, and security context
- **`database-schema.md`** - Database structure and schema information
- **`deployment-guide.md`** - Deployment, CI/CD, and production environment details
- **`development-guide.md`** - Development commands, environment setup, and frontend architecture

## 🔗 Reference Pattern

The main `CLAUDE.md` file uses these references:
```markdown
> **详细信息**: 查看 [.claude/docs/filename.md](.claude/docs/filename.md)
```

## 📖 与主文档的关系

Claude 文档与项目主文档互补使用：

### 获取详细信息
- **完整开发指南**: 查看 [docs/development/](../../docs/development/)
- **详细故障排除**: 查看 [docs/troubleshooting/](../../docs/troubleshooting/)  
- **业务逻辑详情**: 查看 [docs/business/](../../docs/business/)
- **完整API文档**: 查看 [docs/api/](../../docs/api/)
- **部署操作手册**: 查看 [docs/deployment/](../../docs/deployment/)

### 文档导航
- **项目文档中心**: 查看 [docs/README.md](../../docs/README.md)
- **项目完整指南**: 查看 [docs/guides/README.md](../../docs/guides/README.md)

## 📋 Benefits of This Structure

1. **Simplified Main File**: CLAUDE.md remains concise and focused
2. **Modular Information**: Detailed technical information is organized by topic
3. **Easy Navigation**: Clear references and cross-linking between files
4. **Maintained Functionality**: All original information is preserved and accessible
5. **Better Organization**: Related information is grouped together

## 🔧 Maintenance

When updating project information:
1. Update the relevant `.claude/docs/` file for detailed changes
2. Update `CLAUDE.md` only if core summary information changes
3. Maintain the reference links between files