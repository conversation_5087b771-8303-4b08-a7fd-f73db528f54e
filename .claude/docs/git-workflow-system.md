# Git工作流系统设计文档

## 🎯 系统目标
解决Git版本回退时工作区不干净的问题，确保每次操作都安全可靠。

## 🏗️ 系统架构

### 1. Git Hooks（自动保护）
- **post-checkout**: 切换分支后的状态检查和提醒
- **pre-commit**: 提交前的综合质量和安全检查

### 2. 智能脚本（主动操作）
- **git-clean-reset.sh**: 安全回退脚本
- **git-safe-checkout.sh**: 安全切换脚本
- **git-smart-clean.sh**: 智能清理脚本（3个级别）
- **setup-git-workflow.sh**: 一键安装配置

### 3. Git别名（快捷命令）
- `git safe-reset`: 安全回退
- `git safe-checkout`: 安全切换
- `git smart-clean`: 智能清理
- 更多快捷别名（sr, sc, st, ll等）

### 4. <PERSON>命令（/git）
完全重新设计的git-cn.md，包含：
- `--reset`: 安全回退功能
- `--checkout`: 智能切换功能
- `--clean`: 深度清理功能
- `--stash`: stash管理
- 更多高级功能

## 🚀 快速开始

### 安装
```bash
# 一键安装所有功能
./scripts/setup-git-workflow.sh
```

### 日常使用
```bash
# 安全回退到历史版本
git safe-reset 7613068
# 或使用Claude命令
/git --reset 7613068

# 安全切换分支
git safe-checkout feature/new-feature
# 或使用Claude命令
/git --checkout feature/new-feature

# 定期清理
git smart-clean 2
# 或使用Claude命令
/git --clean
```

## 🛡️ 安全特性

### 1. 自动备份
- 所有破坏性操作前自动stash
- 保留操作记录便于恢复
- 失败时提供回滚选项

### 2. 状态检查
- 操作前检查工作区状态
- 操作后验证结果
- 实时显示进度和状态

### 3. 智能提醒
- 检测潜在问题
- 提供操作建议
- 警告危险操作

## 📊 清理级别说明

### 级别1：基础清理（日常）
- 保存当前工作
- 清理未跟踪文件
- 基本垃圾回收

### 级别2：标准清理（每周）
- 包含级别1所有操作
- 清理远程引用
- 压缩reflog
- 清理旧stash
- 优化仓库

### 级别3：深度清理（每月）
- 包含级别2所有操作
- 完全清理reflog
- 深度垃圾回收
- 重新打包对象
- 优化.gitignore

## 💡 最佳实践

### 日常操作
1. 始终使用 `safe-reset` 而非 `git reset --hard`
2. 使用 `safe-checkout` 切换分支
3. 提交前会自动运行质量检查
4. 保持stash数量在10个以内

### 定期维护
1. 每天：使用安全命令操作
2. 每周：运行级别2清理
3. 每月：运行级别3深度清理
4. 定期：查看git-best-practices.md

### 紧急情况
1. 如果误操作：检查stash列表
2. 如果工作区混乱：运行smart-clean
3. 如果仓库过大：运行深度清理
4. 如果不确定：使用--dry-run预览

## 🔧 配置说明

### 环境变量
- `GIT_SAFE_MODE=1`: 启用额外安全检查
- `GIT_AUTO_STASH=1`: 自动stash未提交更改
- `GIT_CLEAN_LEVEL=2`: 默认清理级别

### Git配置
查看 `.gitconfig.local` 了解所有别名和配置。

### Hooks配置
- 可以临时禁用：`git commit --no-verify`
- 可以测试hooks：`/git --hooks test`

## 📝 故障排除

### 常见问题

**Q: 回退后仍有未提交的文件？**
A: 运行 `git smart-clean 1` 或 `/git --clean`

**Q: stash太多找不到需要的？**
A: 使用 `git sl` 查看带时间的列表

**Q: 想要更激进的清理？**
A: 使用 `git smart-clean 3`（谨慎）

**Q: hooks影响性能？**
A: 可以优化或临时禁用特定检查

## 🔄 更新日志

### v1.0.0 (2024-01-29)
- 初始版本发布
- 实现核心安全功能
- 完整的hooks系统
- 智能清理脚本
- Claude命令集成

## 📚 相关文档
- [Git最佳实践](./git-best-practices.md)
- [Claude命令参考](../commands/git-cn.md)
- [项目开发指南](./development-guide.md)

---
*智能Git工作流系统 - 让版本控制更安全、更高效*