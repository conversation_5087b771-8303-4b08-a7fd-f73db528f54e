# Development Guide

## 🛠️ Common Commands

### Backend Development
```bash
# Build all modules
mvn clean package

# Run tests
mvn test

# Run specific module
mvn spring-boot:run -pl api-gateway
```

### Frontend Development
```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build

# Run code quality checks
npm run quality:check
```

### Docker Operations
```bash
# Build and start services
docker-compose up -d

# Use local images (development)
docker-compose -f docker-compose.local.yml up -d

# View logs
docker-compose logs -f

# Health check
curl http://localhost:8080/actuator/health
```

## 🖥️ Development Environment
- **Local Development**: Use `docker-compose.local.yml` for local image priority
- **Build Commands**: `mvn clean package` for backend, `npm run build` for frontend
- **Code Quality**: ESLint, Prettier, and type checking configured

## 📊 Frontend Architecture

### React Application Structure
- **Material-UI v5**: Custom MD-prefixed components
- **Chart.js**: Data visualization with custom configurations
- **React Router v6**: Client-side routing with protected routes
- **Context API**: Global state management for auth and UI
- **Axios**: HTTP client with interceptors for auth and error handling

### Key Components
- **DashboardLayout**: Main application layout with responsive sidebar
- **GenericDataTable**: Reusable table component with pagination
- **DebtStatisticsChart**: Chart.js wrapper for debt analytics
- **Authentication forms**: Login/signup with validation