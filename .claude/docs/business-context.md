# Business Context Reference

## 🎯 Primary Business Functions
1. **Debt Lifecycle Management**: From recording to disposal
2. **Multi-dimensional Reporting**: Company, time-based, category analysis
3. **Data Integrity Assurance**: Cross-table validation and consistency
4. **Flexible Export Capabilities**: Excel reports with custom filtering
5. **Role-based Security**: Granular permission control
6. **Audit Trail**: Complete transaction history and change tracking
7. **Integration Ready**: APIs for external system connectivity

## 🏢 Key Business Entities
- **债权人 (Creditor)**: Entity owed money
- **债务人 (Debtor)**: Entity owing money
- **逾期债权 (Overdue Debt)**: Past-due financial obligations
- **处置 (Disposal)**: Actions taken to resolve debt
- **减值准备 (Impairment Reserve)**: Accounting provisions for bad debt
- **诉讼/非诉讼 (Litigation/Non-litigation)**: Legal vs non-legal debt recovery

## 🔒 Security & Authentication

### Authentication Features
- **JWT-based authentication** with 24-hour token expiry
- **Role-based access control** (ADMIN, USER, VIEWER)
- **Multi-datasource security** with proper user isolation
- **CORS configuration** handled by Spring Security

### Authorization Levels
- **Public Access**: Login, health checks, Swagger documentation
- **Authenticated Users**: Basic debt management operations
- **Admin Users**: User management, system monitoring, audit logs
- **Export Users**: Data export functionality

## 📚 Documentation Resources

### Key Documentation Files
- **API Documentation**: `/docs/api/README.md` - Complete API interface documentation
- **Deployment Guide**: `/docs/deployment/README.md` - Deployment and CI/CD setup
- **Troubleshooting**: `/docs/troubleshooting/README.md` - Common issues and solutions
- **Development Guide**: `/docs/development/README.md` - Development environment setup
- **Business Logic**: `/docs/business/` - Business requirements and logic documentation

### Comprehensive Documentation Structure
- **60+ documentation files** covering all aspects of the system
- **Bilingual documentation** (English and Chinese) for business requirements
- **Architecture diagrams** and technical specifications
- **Integration guides** for external systems (OA, Treasury, Kingdee)
- **Operations runbooks** for production management

## 📖 System Summary
This system handles complex financial relationships with sophisticated business rules, comprehensive reporting, and enterprise-grade security suitable for financial institutions.