# API Endpoints Reference

## 🔌 Authentication (`/api/auth`)
- `POST /api/auth/login` - User authentication with JWT token generation
- `POST /api/auth/reset-password` - Password reset functionality
- `GET /api/protected` - Protected resource access validation

## 💰 Debt Management (`/api/debts`)
- `POST /api/debts/add` - Add new overdue debt records
- `POST /api/debts/update/reduction` - Update debt disposal/reduction data
- `DELETE /api/debts/delete/disposal` - Delete disposal records
- `GET /api/debts/statistics` - Debt statistics with filtering
- `GET /api/debts/search` - Search debt records by creditor/debtor

## 📊 Data Export (`/api/export`)
- `GET /api/export/complete-report` - Export comprehensive reports
- Excel report generation using Apache POI and Aspose.Cells

## 🔍 System Monitoring
- `GET /actuator/health` - System health check
- `GET /api/datamonitor/consistency` - Data consistency check