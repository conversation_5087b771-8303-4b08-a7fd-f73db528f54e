# SuperClaude MCP Integration Details
# Content from MCP.md not in global patterns

## Server_Capabilities_Extended
Context7:
  Purpose: "Official library documentation & code examples | Research standards"
  Capabilities: "resolve-library-id | get-library-docs | version-specific documentation"
  Best_For: "API integration | Framework patterns | Library adoption | Official standards"
  Use: "Research-first methodology | Evidence-based implementation"
  Token_Cost: "Low-Medium | High accuracy | Authoritative sources"
  Workflows: "Library detection → resolve-id → get-docs → implement with citations"

Sequential:
  Purpose: "Multi-step complex problem solving | Architectural thinking | Analysis"
  Capabilities: "sequentialthinking | adaptive reasoning | systematic problem decomposition"
  Best_For: "System design | Root cause analysis | Complex debugging | Architecture review"
  Use: "Deep technical analysis | Evidence-based reasoning | Quality investigation"
  Token_Cost: "Medium-High | Comprehensive analysis | Insights"
  Workflows: "Problem → decompose → analyze → synthesize → recommend"

Magic:
  Purpose: "UI component generation | Design system integration"
  Capabilities: "component-builder | component-refiner | component-inspiration | logo-search"
  Best_For: "React/Vue components | Design systems | UI patterns | Rapid prototyping"
  Use: "Consistent design implementation | Pattern adherence | Quality components"
  Token_Cost: "Medium | High-quality output | Design system compliance"
  Workflows: "Requirements → generate → refine → integrate → validate"

Puppeteer:
  Purpose: "Browser automation | E2E testing | Performance validation"
  Capabilities: "browser-connect | navigation | testing | screenshots | performance-monitoring"
  Best_For: "E2E testing | Performance monitoring | Visual validation | User interaction testing"
  Use: "Quality assurance | Performance validation | User experience testing"
  Token_Cost: "Low (action-based) | High reliability | Testing"
  Workflows: "Connect → navigate → interact → validate → report"

## Token_Economics
Budget_Allocation: "Native(0) | Light_MCP(minimal) | Medium_MCP(moderate) | Heavy_MCP(extensive)"
Intelligent_Escalation: "Native→C7→Sequential→Multi-MCP | Cost-aware progression"
Abort_Conditions: "High context usage | MCP timeout/error | Diminishing returns"
Efficiency_Patterns: "Batch similar operations | Cache results | Progressive loading"

## Workflows
Library_Research:
  Trigger: "External library detection | Import statement analysis | Framework questions"
  Process: "C7 resolve-library-id → validate documentation → extract patterns → implement with citations"
  Standards: "Official documentation required | Version compatibility checked | Best practices documented"
  Example: "'React hooks implementation' → C7('react') → get-docs('hooks') → implementation"

Complex_Analysis:
  Trigger: "Multi-step problems | System design | Architecture questions | Performance issues"
  Process: "Sequential decomposition → guided research → comprehensive analysis → action plan"
  Standards: "Evidence-based reasoning | Methodology | Documented decision process"
  Example: "'Performance optimization' → Sequential(analyze bottlenecks) → C7(optimization patterns) → implementation"

UI_Development:
  Trigger: "Component requirements | Design system needs | Frontend development"
  Process: "Magic component generation → design system integration → quality validation → testing"
  Standards: "Accessibility compliance | Design system adherence | Patterns"
  Example: "'Dashboard components' → Magic('dashboard') → integrate patterns → Puppeteer validation"

Testing:
  Trigger: "Quality assurance needs | E2E testing | Performance validation"
  Process: "Puppeteer automation → comprehensive testing → performance monitoring → quality reports"
  Standards: "Testing patterns | Coverage requirements | Performance standards"
  Example: "'E2E testing' → Puppeteer(test scenarios) → performance monitoring → quality reports"

## Quality_Control
Context7_Validation:
  Success_Criteria: "Relevant documentation found | Official sources confirmed | Version compatibility verified"
  Partial_Results: "Try alternative search terms | Validate with official sources | Document limitations"
  Failure_Recovery: "WebSearch official documentation | Cache partial results | Continue with warnings"

Sequential_Validation:
  Success_Criteria: "Clear analysis provided | Logical step progression | Actionable recommendations"
  Partial_Results: "Use available analysis | Note limitations | Request clarification if needed"
  Failure_Recovery: "Break down problem further | Use native analysis | Document reasoning gaps"

Magic_Validation:
  Success_Criteria: "Component matches requirements | Design system compliance | Accessibility standards"
  Partial_Results: "Component refinement needed | Pattern integration required | Customization needed"
  Failure_Recovery: "Search existing patterns | Manual implementation | Document component requirements"

Puppeteer_Validation:
  Success_Criteria: "Test execution successful | Performance metrics collected | User interactions validated"
  Partial_Results: "Limited testing possible | Performance data incomplete | Interaction issues"
  Failure_Recovery: "Native testing guidance | Manual validation steps | Alternative testing approaches"

## Command_Integration
Development_Commands:
  build: "Magic for UI components | C7 for framework documentation | Sequential for architecture"
  dev_setup: "C7 for tooling documentation | Sequential for environment optimization"
  test: "Puppeteer for E2E testing | C7 for testing frameworks | Sequential for coverage analysis"

Analysis_Commands:
  analyze: "Sequential for complex analysis | C7 for pattern research | Puppeteer for performance"
  troubleshoot: "Sequential for root cause analysis | C7 for solution patterns | Puppeteer for reproduction"
  improve: "Sequential for optimization analysis | C7 for best practices | Puppeteer for validation"
  explain: "C7 for official documentation | Sequential for complex explanations | Magic for examples"

Operations_Commands:
  deploy: "Sequential for deployment analysis | C7 for deployment patterns | Puppeteer for validation"
  scan: "Sequential for comprehensive analysis | C7 for security standards | Native for speed"
  migrate: "Sequential for migration planning | C7 for migration patterns | Puppeteer for validation"
  cleanup: "Sequential for impact analysis | Native for speed | C7 for best practices"

Design_Commands:
  design: "Sequential for architectural thinking | C7 for design patterns | Magic for prototypes"
  spawn: "Intelligent MCP routing based on task type | Expertise matching"
  document: "C7 for documentation standards | Sequential for complex topics | Magic for examples"

## Error_Recovery
Context7_Recovery:
  Library_Not_Found: "Broader search terms → WebSearch official docs → cache alternatives"
  Documentation_Incomplete: "Try specific topics → search recent versions → note limitations"
  API_Timeout: "Cache partial results → continue with native tools → document limitations"
  Version_Conflicts: "Search specific versions → identify compatibility → document requirements"

Sequential_Recovery:
  Analysis_Timeout: "Use partial analysis → note limitations → continue with available insights"
  Token_Limit: "Summarize key findings → focus on critical issues → provide actionable recommendations"
  Complex_Problems: "Break into smaller components → iterative analysis → progressive understanding"
  Unclear_Requirements: "Request clarification → make reasonable assumptions → document assumptions"

Magic_Recovery:
  Component_Generation_Failed: "Search existing patterns → provide template → manual implementation guidance"
  Design_System_Mismatch: "Component refinement → pattern customization → integration guidance"
  Quality_Issues: "Component review → improvement suggestions → alternative approaches"
  Integration_Problems: "Document requirements → provide integration steps → troubleshooting guidance"

Puppeteer_Recovery:
  Browser_Connection_Failed: "Native testing commands → manual testing guidance → validation steps"
  Test_Execution_Issues: "Simplified test scenarios → manual validation → alternative approaches"
  Performance_Monitoring_Failed: "Native performance tools → manual monitoring → metrics guidance"
  Automation_Limitations: "Hybrid testing approach → manual verification → documented procedures"

## Best_Practices
Research_Standards:
  External_Libraries: "Context7 lookup REQUIRED | Official documentation only | Version validation"
  Unknown_Patterns: "Research before implementation | Evidence-based decisions | Source citations"
  Low_Confidence: "Block implementation until research complete | Standards maintained"

Implementation_Standards:
  Source_Attribution: "Document MCP sources | Credit authoritative documentation | Maintain evidence trail"
  Quality_Validation: "Validate before implementation | Test comprehensively | Monitor performance"
  Patterns: "Follow industry standards | Maintain consistency | Evidence-based choices"

Optimization_Guidelines:
  Token_Efficiency: "Match MCP to user need | Set appropriate budgets | Graceful fallbacks"
  Performance_Management: "Monitor response times | Cache successful patterns | Batch similar operations"
  Quality_Assurance: "Validate outputs | Test implementations | Maintain standards"

Workflows:
  Complex_Projects: "Multi-MCP orchestration | Intelligent coordination | Quality integration"
  Simple_Operations: "Native tools preferred | MCP only when value-adding | Cost-conscious decisions"
  Quality_Focus: "Evidence-based standards | Validation | Comprehensive testing"

## Session_Management
Context_Preservation:
  MCP_State: "Active servers | Token usage tracking | Performance monitoring"
  Result_Caching: "Successful patterns | Documentation findings | Component libraries"
  Session_Continuity: "Server health monitoring | Graceful degradation | State recovery"

Performance_Optimization:
  Server_Health: "Regular health checks | Performance monitoring | Load balancing"
  Resource_Management: "Token budget tracking | Cost optimization | Intelligent routing"
  Quality_Monitoring: "Success rate tracking | Error pattern analysis | Continuous improvement"

Standards:
  Evidence_Requirements: "Documentation for all external libraries | Metrics for performance claims"
  Quality_Gates: "Validation before implementation | Testing after deployment | Monitoring in production"
  Research_Methodology: "Official sources required | Evidence-based decisions | Standards"