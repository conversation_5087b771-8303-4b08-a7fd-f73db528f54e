/**
 * Claude Code Notification Hook - 通知桥接系统
 * 连接Claude AI和Git hooks的关键桥梁
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class NotificationBridge {
  constructor() {
    this.projectRoot = process.cwd();
    this.logFile = path.join(this.projectRoot, '.claude', 'logs', 'hooks.log');
    this.configFile = path.join(this.projectRoot, '.claude', 'hooks', 'config.json');
    this.config = this.loadConfig();
  }

  async execute(context) {
    this.log('📢 [Notification] 开始处理通知和Git hooks桥接...');
    
    try {
      const { notification, metadata } = context;
      
      // 1. 解析通知类型和内容
      const parsedNotification = this.parseNotification(notification);
      
      // 2. 根据通知类型执行相应操作
      const result = await this.handleNotification(parsedNotification, metadata);
      
      // 3. 记录操作日志
      await this.logOperation(parsedNotification, result);
      
      // 4. 发送状态更新
      await this.sendStatusUpdate(parsedNotification, result);
      
      this.log('✅ [Notification] 通知处理完成');
      
      return {
        status: 'processed',
        notification: parsedNotification,
        result,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      this.log(`❌ [Notification] 处理失败: ${error.message}`);
      await this.handleError(error, context);
      throw error;
    }
  }

  parseNotification(notification) {
    // 解析不同类型的通知
    const patterns = {
      code_completed: /代码.*(?:完成|生成|修改)/i,
      test_required: /测试.*(?:需要|要求)/i,
      commit_ready: /(?:提交|commit).*(?:准备|ready)/i,
      error_occurred: /(?:错误|error|失败|failed)/i,
      review_needed: /(?:审查|review).*(?:需要|needed)/i
    };
    
    let type = 'unknown';
    let priority = 'normal';
    
    // 检测通知类型
    for (const [notificationType, pattern] of Object.entries(patterns)) {
      if (pattern.test(notification)) {
        type = notificationType;
        break;
      }
    }
    
    // 设置优先级
    if (type === 'error_occurred') {
      priority = 'high';
    } else if (type === 'commit_ready') {
      priority = 'high';
    }
    
    return {
      type,
      priority,
      originalMessage: notification,
      timestamp: new Date().toISOString(),
      processed: false
    };
  }

  async handleNotification(parsedNotification, metadata) {
    this.log(`🔄 处理通知类型: ${parsedNotification.type}`);
    
    switch (parsedNotification.type) {
      case 'code_completed':
        return await this.handleCodeCompleted(parsedNotification, metadata);
      
      case 'commit_ready':
        return await this.handleCommitReady(parsedNotification, metadata);
      
      case 'error_occurred':
        return await this.handleError(parsedNotification, metadata);
      
      case 'test_required':
        return await this.handleTestRequired(parsedNotification, metadata);
      
      case 'review_needed':
        return await this.handleReviewNeeded(parsedNotification, metadata);
      
      default:
        return await this.handleGenericNotification(parsedNotification, metadata);
    }
  }

  async handleCodeCompleted(notification, metadata) {
    this.log('🎯 处理代码完成通知');
    
    const result = {
      action: 'code_completed',
      gitOperations: [],
      nextSteps: []
    };
    
    // 1. 检查是否应该自动提交
    if (this.shouldAutoCommit(metadata)) {
      this.log('🚀 启动自动提交流程...');
      
      // 设置环境变量供Git hooks使用
      process.env.CLAUDE_AI_COMMIT = 'true';
      process.env.CLAUDE_COMMIT_CONTEXT = JSON.stringify(metadata);
      
      // 生成智能提交信息
      const commitMessage = this.generateCommitMessage(metadata);
      process.env.CLAUDE_COMMIT_MESSAGE = commitMessage;
      
      try {
        // 暂存变更
        execSync('git add .', { stdio: 'pipe' });
        result.gitOperations.push('staged_changes');
        
        // 触发Git提交（这会启动Git hooks链）
        const commitResult = await this.triggerGitCommit(commitMessage);
        result.gitOperations.push('commit_triggered');
        result.commitHash = commitResult.hash;
        
      } catch (error) {
        this.log(`⚠️ 自动提交失败: ${error.message}`);
        result.error = error.message;
        result.nextSteps.push('manual_intervention_required');
        
        // 启动自动修复流程
        await this.startAutoFixLoop(error, metadata);
      }
    } else {
      this.log('📝 代码完成，等待手动提交');
      result.nextSteps.push('manual_commit_required');
    }
    
    return result;
  }

  async handleCommitReady(notification, metadata) {
    this.log('📦 处理提交就绪通知');
    
    const commitMessage = this.generateCommitMessage(metadata);
    
    try {
      const commitResult = await this.triggerGitCommit(commitMessage);
      
      return {
        action: 'commit_executed',
        commitHash: commitResult.hash,
        message: commitMessage,
        success: true
      };
      
    } catch (error) {
      return {
        action: 'commit_failed',
        error: error.message,
        autoFixTriggered: await this.startAutoFixLoop(error, metadata)
      };
    }
  }

  async triggerGitCommit(message) {
    return new Promise((resolve, reject) => {
      // 使用spawn来更好地控制Git提交过程
      const gitCommit = spawn('git', ['commit', '-m', message], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });
      
      let stdout = '';
      let stderr = '';
      
      gitCommit.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      gitCommit.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      gitCommit.on('close', (code) => {
        if (code === 0) {
          // 提取commit hash
          const hashMatch = stdout.match(/\\[\\w+\\s+([a-f0-9]+)\\]/);
          const hash = hashMatch ? hashMatch[1] : 'unknown';
          
          this.log(`✅ Git提交成功: ${hash}`);
          resolve({ success: true, hash, output: stdout });
        } else {
          this.log(`❌ Git提交失败 (代码: ${code}): ${stderr}`);
          reject(new Error(`Git commit failed: ${stderr}`));
        }
      });
      
      gitCommit.on('error', (error) => {
        this.log(`❌ Git命令执行错误: ${error.message}`);
        reject(error);
      });
    });
  }

  async startAutoFixLoop(error, metadata) {
    this.log('🔧 启动自动修复循环...');
    
    const fixScript = path.join(this.projectRoot, '.claude', 'hooks', 'auto-fix-loop.js');
    
    if (fs.existsSync(fixScript)) {
      try {
        // 异步启动修复循环，不阻塞当前流程
        const fixProcess = spawn('node', [fixScript], {
          detached: true,
          stdio: 'ignore',
          env: {
            ...process.env,
            CLAUDE_FIX_ERROR: JSON.stringify({
              error: error.message,
              metadata,
              timestamp: new Date().toISOString()
            })
          }
        });
        
        fixProcess.unref();
        
        this.log('🔄 自动修复流程已在后台启动');
        return true;
        
      } catch (fixError) {
        this.log(`❌ 启动自动修复失败: ${fixError.message}`);
        return false;
      }
    } else {
      this.log('⚠️ 自动修复脚本不存在');
      return false;
    }
  }

  generateCommitMessage(metadata) {
    const { tool, changes, context } = metadata || {};
    
    // 智能生成提交信息
    let message = '';
    
    if (tool) {
      switch (tool.name) {
        case 'Edit':
        case 'MultiEdit':
          message = `fix: ${this.summarizeChanges(changes)}`;
          break;
        case 'Write':
          message = `feat: ${this.summarizeNewFiles(changes)}`;
          break;
        default:
          message = `update: AI代码更新`;
      }
    } else {
      message = 'feat: AI自动化代码更新';
    }
    
    // 添加AI标识
    message += `\\n\\n🤖 Generated with Claude Code\\nCo-Authored-By: Claude <<EMAIL>>`;
    
    return message;
  }

  shouldAutoCommit(metadata) {
    // 检查配置和元数据，决定是否自动提交
    const config = this.config.auto_commit || {};
    
    if (!config.enabled) {
      return false;
    }
    
    // 检查分支限制
    const currentBranch = this.getCurrentBranch();
    const allowedBranches = config.allowed_branches || ['feature/*', 'develop'];
    
    const isAllowed = allowedBranches.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace('*', '.*'));
        return regex.test(currentBranch);
      }
      return pattern === currentBranch;
    });
    
    return isAllowed;
  }

  async logOperation(notification, result) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      notification,
      result,
      branch: this.getCurrentBranch(),
      commit: this.getCurrentCommit()
    };
    
    const operationLogFile = path.join(this.projectRoot, '.claude', 'logs', 'operations.jsonl');
    
    try {
      const logDir = path.dirname(operationLogFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      fs.appendFileSync(operationLogFile, JSON.stringify(logEntry) + '\\n');
    } catch (error) {
      this.log(`⚠️ 操作日志写入失败: ${error.message}`);
    }
  }

  async sendStatusUpdate(notification, result) {
    // 发送状态更新到配置的通知渠道
    const notificationConfig = this.config.notifications || {};
    
    if (notificationConfig.slack_webhook) {
      await this.sendSlackUpdate(notification, result, notificationConfig.slack_webhook);
    }
    
    if (notificationConfig.webhook_url) {
      await this.sendWebhookUpdate(notification, result, notificationConfig.webhook_url);
    }
  }

  // 辅助方法
  loadConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        return JSON.parse(fs.readFileSync(this.configFile, 'utf8'));
      }
    } catch (error) {
      this.log(`⚠️ 配置文件加载失败: ${error.message}`);
    }
    
    // 返回默认配置
    return {
      auto_commit: {
        enabled: true,
        allowed_branches: ['feature/*', 'develop', 'hotfix/*']
      },
      notifications: {
        enabled: true
      }
    };
  }

  getCurrentBranch() {
    try {
      return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return 'unknown';
    }
  }

  getCurrentCommit() {
    try {
      return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return 'unknown';
    }
  }

  summarizeChanges(changes) {
    if (!changes || !changes.length) {
      return '更新代码逻辑';
    }
    
    const fileCount = changes.length;
    if (fileCount === 1) {
      return `更新 ${path.basename(changes[0].file)}`;
    } else {
      return `更新 ${fileCount} 个文件`;
    }
  }

  summarizeNewFiles(changes) {
    if (!changes || !changes.length) {
      return '添加新文件';
    }
    
    return `添加 ${changes.map(c => path.basename(c.file)).join(', ')}`;
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    
    console.log(logMessage);
    
    try {
      const logDir = path.dirname(this.logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      fs.appendFileSync(this.logFile, logMessage + '\\n');
    } catch (error) {
      // 忽略日志写入错误
    }
  }
}

// 导出主函数
module.exports = async function(context) {
  const bridge = new NotificationBridge();
  return await bridge.execute(context);
};

// 如果直接运行此脚本
if (require.main === module) {
  const bridge = new NotificationBridge();
  
  // 测试通知处理
  const testNotification = {
    notification: '代码生成完成，准备提交',
    metadata: {
      tool: { name: 'Edit' },
      changes: [{ file: 'test.js' }],
      context: { branch: 'feature/test' }
    }
  };
  
  bridge.execute(testNotification)
    .then(result => {
      console.log('✅ 通知处理完成:', JSON.stringify(result, null, 2));
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 通知处理失败:', error.message);
      process.exit(1);
    });
}