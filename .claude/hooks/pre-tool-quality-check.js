/**
 * Claude Code PreToolUse Hook - AI工具执行前质量预检
 * 与传统Git hooks集成的第一层检查
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class PreToolQualityCheck {
  constructor() {
    this.projectRoot = process.cwd();
    this.logFile = path.join(this.projectRoot, '.claude', 'logs', 'hooks.log');
    this.baseline = {};
  }

  async execute(context) {
    this.log('🔍 [PreToolUse] 开始AI工具执行前质量预检...');
    
    try {
      // 1. 检查工作区状态
      await this.checkWorkspaceStatus();
      
      // 2. 验证分支策略
      await this.validateBranchStrategy();
      
      // 3. 检查依赖状态
      await this.checkDependencies();
      
      // 4. 设置质量基线
      await this.captureQualityBaseline();
      
      // 5. 验证环境配置
      await this.validateEnvironment();
      
      this.log('✅ [PreToolUse] 质量预检通过');
      
      return {
        status: 'passed',
        baseline: this.baseline,
        timestamp: new Date().toISOString(),
        context: {
          branch: this.getCurrentBranch(),
          workspaceClean: this.isWorkspaceClean(),
          dependenciesOk: true
        }
      };
      
    } catch (error) {
      this.log(`❌ [PreToolUse] 预检失败: ${error.message}`);
      throw error;
    }
  }

  async checkWorkspaceStatus() {
    const status = execSync('git status --porcelain', { encoding: 'utf8' });
    
    if (status.trim()) {
      this.log('⚠️ 工作区有未提交的变更，自动暂存...');
      
      // 暂存当前变更
      execSync('git stash push -m "PreToolUse auto-stash $(date)"');
      this.log('📦 变更已暂存');
    }
    
    return { clean: !status.trim() };
  }

  async validateBranchStrategy() {
    const currentBranch = this.getCurrentBranch();
    
    // 定义允许AI操作的分支模式
    const allowedPatterns = [
      /^feature\/.+/,
      /^hotfix\/.+/,
      /^develop$/,
      /^bugfix\/.+/
    ];
    
    const isAllowed = allowedPatterns.some(pattern => pattern.test(currentBranch));
    
    if (!isAllowed) {
      throw new Error(`当前分支 "${currentBranch}" 不允许AI直接修改。请切换到 feature/* 或 develop 分支。`);
    }
    
    this.log(`✅ 分支验证通过: ${currentBranch}`);
    return { branch: currentBranch, allowed: true };
  }

  async checkDependencies() {
    const packageJsonPath = path.join(this.projectRoot, 'package.json');
    const pomXmlPath = path.join(this.projectRoot, 'pom.xml');
    
    let checks = [];
    
    // 检查 Node.js 依赖
    if (fs.existsSync(packageJsonPath)) {
      try {
        execSync('npm ls --depth=0', { stdio: 'pipe' });
        checks.push({ type: 'npm', status: 'ok' });
      } catch (error) {
        this.log('⚠️ NPM依赖检查失败，尝试自动修复...');
        execSync('npm install', { stdio: 'pipe' });
        checks.push({ type: 'npm', status: 'fixed' });
      }
    }
    
    // 检查 Maven 依赖
    if (fs.existsSync(pomXmlPath)) {
      try {
        execSync('mvn dependency:resolve -q', { stdio: 'pipe' });
        checks.push({ type: 'maven', status: 'ok' });
      } catch (error) {
        this.log('⚠️ Maven依赖检查失败');
        checks.push({ type: 'maven', status: 'error', error: error.message });
      }
    }
    
    this.log('✅ 依赖检查完成');
    return { checks };
  }

  async captureQualityBaseline() {
    this.baseline = {
      timestamp: new Date().toISOString(),
      branch: this.getCurrentBranch(),
      commit: this.getCurrentCommit(),
      metrics: {}
    };
    
    // 捕获代码质量基线
    try {
      // 获取测试覆盖率
      if (fs.existsSync(path.join(this.projectRoot, 'package.json'))) {
        const coverage = this.getCoverageBaseline();
        this.baseline.metrics.coverage = coverage;
      }
      
      // 获取代码行数
      const loc = this.getCodeLinesBaseline();
      this.baseline.metrics.linesOfCode = loc;
      
      // 获取文件数量
      const fileCount = this.getFileCountBaseline();
      this.baseline.metrics.fileCount = fileCount;
      
    } catch (error) {
      this.log(`⚠️ 基线捕获部分失败: ${error.message}`);
    }
    
    // 保存基线到文件
    const baselineFile = path.join(this.projectRoot, '.claude', 'baseline.json');
    fs.writeFileSync(baselineFile, JSON.stringify(this.baseline, null, 2));
    
    this.log('📊 质量基线已保存');
    return this.baseline;
  }

  async validateEnvironment() {
    const env = process.env;
    const requiredEnvVars = ['NODE_ENV'];
    
    for (const envVar of requiredEnvVars) {
      if (!env[envVar]) {
        this.log(`⚠️ 环境变量 ${envVar} 未设置，使用默认值`);
        process.env[envVar] = 'development';
      }
    }
    
    // 检查Claude特定环境变量
    if (!env.CLAUDE_AI_GITFLOW) {
      process.env.CLAUDE_AI_GITFLOW = 'enabled';
      this.log('🤖 启用Claude AI GitFlow模式');
    }
    
    return { validated: true };
  }

  // 辅助方法
  getCurrentBranch() {
    try {
      return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return 'unknown';
    }
  }

  getCurrentCommit() {
    try {
      return execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      return 'unknown';
    }
  }

  isWorkspaceClean() {
    try {
      const status = execSync('git status --porcelain', { encoding: 'utf8' });
      return !status.trim();
    } catch (error) {
      return false;
    }
  }

  getCoverageBaseline() {
    try {
      // 这里可以集成实际的覆盖率工具
      return { total: 0, unit: 0, integration: 0 };
    } catch (error) {
      return null;
    }
  }

  getCodeLinesBaseline() {
    try {
      const result = execSync('find . -name "*.js" -o -name "*.ts" -o -name "*.java" | xargs wc -l | tail -1', 
        { encoding: 'utf8' });
      const lines = parseInt(result.trim().split(' ')[0]) || 0;
      return lines;
    } catch (error) {
      return 0;
    }
  }

  getFileCountBaseline() {
    try {
      const result = execSync('find . -type f \\( -name "*.js" -o -name "*.ts" -o -name "*.java" \\) | wc -l', 
        { encoding: 'utf8' });
      return parseInt(result.trim()) || 0;
    } catch (error) {
      return 0;
    }
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    
    console.log(logMessage);
    
    // 写入日志文件
    try {
      const logDir = path.dirname(this.logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      fs.appendFileSync(this.logFile, logMessage + '\n');
    } catch (error) {
      // 忽略日志写入错误
    }
  }
}

// 导出主函数
module.exports = async function(context) {
  const checker = new PreToolQualityCheck();
  return await checker.execute(context);
};

// 如果直接运行此脚本
if (require.main === module) {
  const checker = new PreToolQualityCheck();
  checker.execute({})
    .then(result => {
      console.log('✅ PreToolUse检查完成:', JSON.stringify(result, null, 2));
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ PreToolUse检查失败:', error.message);
      process.exit(1);
    });
}