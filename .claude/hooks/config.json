{"version": "1.0.0", "description": "AI-GitFlow混合Hooks系统配置", "claude_hooks": {"enabled": true, "hooks": {"PreToolUse": {"enabled": true, "script": ".claude/hooks/pre-tool-quality-check.js", "timeout": 30000, "required": true}, "PostToolUse": {"enabled": true, "script": ".claude/hooks/post-tool-validation.js", "timeout": 60000, "required": true}, "Notification": {"enabled": true, "script": ".claude/hooks/notification-bridge.js", "timeout": 10000, "required": true}, "Stop": {"enabled": true, "script": ".claude/hooks/final-quality-gate.js", "timeout": 15000, "required": false}}}, "git_hooks": {"enabled": true, "integration_mode": "hybrid", "hooks": {"pre-commit": {"enabled": true, "ai_enhanced": true, "checks": ["syntax", "lint", "test", "security"]}, "post-commit": {"enabled": true, "triggers": ["integration_test", "notification"]}, "pre-receive": {"enabled": true, "server_side": true}, "post-receive": {"enabled": true, "triggers": ["automated_testing_loop", "deployment"]}}}, "auto_commit": {"enabled": true, "allowed_branches": ["feature/*", "hotfix/*", "develop", "bugfix/*"], "protected_branches": ["main", "master", "release/*", "production"], "quality_threshold": 0.8, "max_fix_attempts": 3, "commit_message_template": "{type}: {description}\n\n🤖 Generated with <PERSON>\nCo-Authored-By: <PERSON> <<EMAIL>>"}, "quality_gates": {"pre_commit": {"syntax_check": true, "type_check": true, "lint_check": true, "unit_tests": true, "security_scan": true, "coverage_threshold": 0.7}, "pre_merge": {"integration_tests": true, "performance_tests": false, "e2e_tests": false, "documentation_check": true, "coverage_threshold": 0.8}, "pre_release": {"full_test_suite": true, "security_audit": true, "performance_audit": true, "documentation_complete": true, "coverage_threshold": 0.9}}, "auto_fix": {"enabled": true, "max_attempts": 3, "timeout": 300000, "strategies": {"syntax_errors": {"enabled": true, "ai_fix": true, "fallback": "human_intervention"}, "test_failures": {"enabled": true, "ai_fix": true, "generate_missing_tests": true}, "lint_errors": {"enabled": true, "auto_fix": true, "ai_fix": false}, "security_issues": {"enabled": true, "ai_fix": true, "require_human_review": true}}}, "notifications": {"enabled": true, "channels": {"console": {"enabled": true, "level": "info"}, "file": {"enabled": true, "path": ".claude/logs/notifications.log", "level": "debug"}, "slack": {"enabled": false, "webhook_url": "", "channel": "#dev-ai-commits", "level": "warning"}, "email": {"enabled": false, "smtp_server": "", "recipients": [], "level": "error"}}, "events": {"commit_success": true, "commit_failure": true, "auto_fix_started": true, "auto_fix_success": true, "auto_fix_failure": true, "human_intervention_required": true, "quality_threshold_exceeded": true}}, "logging": {"enabled": true, "level": "info", "files": {"hooks": ".claude/logs/hooks.log", "operations": ".claude/logs/operations.jsonl", "errors": ".claude/logs/errors.log", "performance": ".claude/logs/performance.log"}, "rotation": {"enabled": true, "max_size": "10MB", "max_files": 5}}, "performance": {"monitoring": {"enabled": true, "metrics": ["hook_execution_time", "test_execution_time", "ai_processing_time", "git_operation_time"]}, "optimization": {"parallel_execution": true, "cache_dependencies": true, "incremental_testing": true}, "thresholds": {"hook_timeout": 120000, "total_pipeline_timeout": 600000, "warning_threshold": 60000}}, "security": {"ai_permissions": {"allowed_operations": ["read_files", "write_files", "execute_tests", "git_operations"], "restricted_operations": ["system_commands", "network_access", "sensitive_env_vars"]}, "code_analysis": {"scan_for_secrets": true, "dependency_audit": true, "vulnerability_check": true}}, "branch_policies": {"main": {"ai_commits": false, "require_pr": true, "require_reviews": 2, "quality_threshold": 0.95, "auto_fix": false}, "develop": {"ai_commits": true, "require_pr": false, "quality_threshold": 0.8, "auto_fix": true}, "feature/*": {"ai_commits": true, "require_pr": false, "quality_threshold": 0.7, "auto_fix": true, "fast_track": true}, "hotfix/*": {"ai_commits": true, "require_pr": false, "quality_threshold": 0.85, "auto_fix": true, "priority": "high"}, "release/*": {"ai_commits": false, "require_pr": true, "require_reviews": 3, "quality_threshold": 0.95, "auto_fix": false}}, "integration": {"ci_cd": {"enabled": true, "platforms": ["github_actions", "jenkins"], "trigger_on_commit": true, "trigger_on_merge": true}, "issue_tracking": {"enabled": false, "platform": "jira", "auto_link_commits": true}, "code_review": {"enabled": true, "platform": "github", "auto_request_review": true, "reviewers": ["@team/senior-devs"]}}, "experimental": {"enabled": false, "features": {"ai_code_review": false, "predictive_testing": false, "intelligent_merging": false, "auto_documentation": false}}}