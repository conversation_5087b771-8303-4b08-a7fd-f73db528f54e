package com.laoshu198838.controller.data;

import com.laoshu198838.service.CacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 缓存管理控制器
 * 提供缓存管理相关的API接口
 * 
 * <AUTHOR>
 */
@Tag(name = "缓存管理", description = "缓存管理相关API")
@RestController
@RequestMapping("/api/admin/cache")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class CacheController {
    
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private CacheManager cacheManager;
    
    /**
     * 获取缓存统计信息
     */
    @Operation(summary = "获取缓存统计信息", description = "获取Redis缓存的统计信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        try {
            CacheService.CacheStats stats = cacheService.getCacheStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("totalKeys", stats.getTotalKeys());
            response.put("userKeys", stats.getUserKeys());
            response.put("debtKeys", stats.getDebtKeys());
            response.put("reportKeys", stats.getReportKeys());
            response.put("configKeys", stats.getConfigKeys());
            response.put("otherKeys", stats.getOtherKeys());
            
            // 获取缓存管理器信息
            response.put("cacheNames", cacheManager.getCacheNames());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 清空所有缓存
     */
    @Operation(summary = "清空所有缓存", description = "清空Redis中的所有缓存数据")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "清空成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/flush-all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> flushAllCache() {
        try {
            cacheService.flushAll();
            
            // 同时清空Spring Cache Manager中的缓存
            cacheManager.getCacheNames().forEach(cacheName -> {
                Objects.requireNonNull(cacheManager.getCache(cacheName)).clear();
            });
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "所有缓存已清空");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 根据前缀清空缓存
     */
    @Operation(summary = "根据前缀清空缓存", description = "根据指定前缀清空相关的缓存数据")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "清空成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/flush-by-prefix")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> flushCacheByPrefix(
            @Parameter(description = "缓存前缀", required = true)
            @RequestParam String prefix) {
        try {
            if (prefix == null || prefix.trim().isEmpty()) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "前缀不能为空");
                return ResponseEntity.badRequest().body(error);
            }
            
            cacheService.flushByPrefix(prefix);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "前缀为 '" + prefix + "' 的缓存已清空");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 清空指定缓存
     */
    @Operation(summary = "清空指定缓存", description = "清空指定名称的缓存")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "清空成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/flush-cache/{cacheName}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> flushCache(
            @Parameter(description = "缓存名称", required = true)
            @PathVariable String cacheName) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "缓存 '" + cacheName + "' 已清空");
                
                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "缓存 '" + cacheName + "' 不存在");
                return ResponseEntity.badRequest().body(error);
            }
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 检查缓存键是否存在
     */
    @Operation(summary = "检查缓存键是否存在", description = "检查指定的缓存键是否存在")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "检查成功"),
            @ApiResponse(responseCode = "400", description = "参数无效"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/exists")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> checkCacheExists(
            @Parameter(description = "缓存键", required = true)
            @RequestParam String key) {
        try {
            if (key == null || key.trim().isEmpty()) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "缓存键不能为空");
                return ResponseEntity.badRequest().body(error);
            }
            
            boolean exists = cacheService.exists(key);
            long expire = cacheService.getExpire(key);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("key", key);
            response.put("exists", exists);
            response.put("expire", expire);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 获取缓存配置信息
     */
    @Operation(summary = "获取缓存配置信息", description = "获取当前的缓存配置信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/config")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getCacheConfig() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("cacheNames", cacheManager.getCacheNames());
            response.put("cacheType", "Redis");
            response.put("defaultTtl", "10 minutes");
            
            Map<String, String> cacheTtls = new HashMap<>();
            cacheTtls.put("users", "30 minutes");
            cacheTtls.put("debts", "5 minutes");
            cacheTtls.put("reports", "15 minutes");
            cacheTtls.put("config", "60 minutes");
            cacheTtls.put("permissions", "20 minutes");
            cacheTtls.put("dictionary", "120 minutes");
            
            response.put("cacheTtls", cacheTtls);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
}
