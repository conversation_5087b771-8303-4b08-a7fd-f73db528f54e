package com.laoshu198838.controller.data;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.laoshu198838.service.DebtManagementService;

/**
 * Excel导出控制器
 * 提供债权明细表导出功能
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/export")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:5173", "http://localhost:8080"}, allowCredentials = "true")
public class ExcelExportController {

    private final DebtManagementService debtManagementService;

    public ExcelExportController(DebtManagementService debtManagementService) {
        this.debtManagementService = debtManagementService;
    }

    @GetMapping("/NewDebtDetails")
    public ResponseEntity<byte[]> exportNewDebtDetails(
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String month,
            @RequestParam(required = false) String company) {
        return debtManagementService.exportNewDebtDetails(year, month, company);
    }

    @GetMapping("/ReductionDebtDetails")
    public ResponseEntity<byte[]> exportReductionDebtDetails(
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String month,
            @RequestParam(required = false) String company) {
        return debtManagementService.exportReductionDebtDetails(year, month, company);
    }

    /**
     * 导出完整的逾期债权清收统计表
     * 包含8个子表的完整报表，支持年份、月份和金额限制参数
     */
    @GetMapping("/completeOverdueReport")
    public ResponseEntity<byte[]> exportCompleteOverdueReport(
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String month,
            @RequestParam(required = false) String amount) {
        return debtManagementService.exportCompleteOverdueReport(year, month, amount);
    }

    /**
     * 临时测试接口：导出完整的逾期债权清收统计表（无认证）
     * 仅用于调试，生产环境应删除此接口
     */
    @GetMapping("/test/completeOverdueReport")
    public ResponseEntity<byte[]> testExportCompleteOverdueReport(
            @RequestParam(required = false, defaultValue = "2025") String year,
            @RequestParam(required = false, defaultValue = "2") String month,
            @RequestParam(required = false, defaultValue = "10") String amount) {
        return debtManagementService.exportCompleteOverdueReport(year, month, amount);
    }
}