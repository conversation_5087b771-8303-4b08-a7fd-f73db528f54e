package com.laoshu198838.controller;

import com.laoshu198838.dto.asset.AssetBasicInfoDTO;
import com.laoshu198838.entity.asset.AssetBasicInfo;
import com.laoshu198838.service.AssetBasicInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资产基本信息控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/assets")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class AssetBasicInfoController {

    private static final Logger logger = LoggerFactory.getLogger(AssetBasicInfoController.class);

    @Autowired
    private AssetBasicInfoService assetBasicInfoService;

    /**
     * 创建资产
     */
    @PostMapping
    public ResponseEntity<AssetBasicInfoDTO> createAsset(@RequestBody AssetBasicInfoDTO dto) {
        try {
            String currentUser = getCurrentUser();
            AssetBasicInfoDTO result = assetBasicInfoService.createAsset(dto, currentUser);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("创建资产失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新资产
     */
    @PutMapping("/{id}")
    public ResponseEntity<AssetBasicInfoDTO> updateAsset(@PathVariable Long id, @RequestBody AssetBasicInfoDTO dto) {
        try {
            String currentUser = getCurrentUser();
            AssetBasicInfoDTO result = assetBasicInfoService.updateAsset(id, dto, currentUser);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("更新资产失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除资产
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAsset(@PathVariable Long id) {
        try {
            String currentUser = getCurrentUser();
            assetBasicInfoService.deleteAsset(id, currentUser);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("删除资产失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据ID查询资产
     */
    @GetMapping("/{id}")
    public ResponseEntity<AssetBasicInfoDTO> getAssetById(@PathVariable Long id) {
        try {
            AssetBasicInfoDTO result = assetBasicInfoService.getAssetById(id);
            if (result != null) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("查询资产失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 分页查询资产列表
     */
    @GetMapping
    public ResponseEntity<Page<AssetBasicInfoDTO>> getAssets(
            @RequestParam(required = false) String managementCompany,
            @RequestParam(required = false) AssetBasicInfo.AssetType assetType,
            @RequestParam(required = false) Long propertyOwnerId,
            @RequestParam(required = false) Boolean hasPropertyCertificate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<AssetBasicInfoDTO> result;
            
            if (managementCompany != null || assetType != null || propertyOwnerId != null || hasPropertyCertificate != null) {
                result = assetBasicInfoService.getAssetsByConditions(
                    managementCompany, assetType, propertyOwnerId, hasPropertyCertificate, pageable);
            } else {
                // 如果没有指定管理公司，使用默认值（这里需要根据用户权限确定）
                String defaultManagementCompany = getDefaultManagementCompany();
                result = assetBasicInfoService.getAssetsByPage(defaultManagementCompany, pageable);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("查询资产列表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 查询瑕疵资产
     */
    @GetMapping("/defective")
    public ResponseEntity<List<AssetBasicInfoDTO>> getDefectiveAssets(
            @RequestParam(required = false) String managementCompany) {
        try {
            String company = managementCompany != null ? managementCompany : getDefaultManagementCompany();
            List<AssetBasicInfoDTO> result = assetBasicInfoService.getDefectiveAssets(company);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("查询瑕疵资产失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取资产类型统计
     */
    @GetMapping("/statistics/type")
    public ResponseEntity<Map<String, Object>> getAssetTypeStatistics(
            @RequestParam(required = false) String managementCompany) {
        try {
            String company = managementCompany != null ? managementCompany : getDefaultManagementCompany();
            Map<String, Object> result = assetBasicInfoService.getAssetTypeStatistics(company);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取资产类型统计失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取瑕疵资产统计
     */
    @GetMapping("/statistics/defective")
    public ResponseEntity<List<Map<String, Object>>> getDefectiveAssetStatistics() {
        try {
            List<Map<String, Object>> result = assetBasicInfoService.getDefectiveAssetStatistics();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取瑕疵资产统计失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取管理公司列表
     */
    @GetMapping("/management-companies")
    public ResponseEntity<List<String>> getManagementCompanies() {
        try {
            List<String> result = assetBasicInfoService.getManagementCompanies();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取管理公司列表失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("Asset Management API is working!");
    }

    /**
     * 获取当前用户（临时实现）
     */
    private String getCurrentUser() {
        // TODO: 从Security Context或JWT Token中获取当前用户
        return "system";
    }

    /**
     * 获取默认管理公司（临时实现）
     */
    private String getDefaultManagementCompany() {
        // TODO: 根据用户权限确定默认管理公司
        return "深圳万润科技股份有限公司";
    }
}
