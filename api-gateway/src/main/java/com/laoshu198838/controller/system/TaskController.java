package com.laoshu198838.controller.system;

import com.laoshu198838.service.OverdueDebtUpdateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务控制器 - 用于手动触发定时任务
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/tasks")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:5173", "http://localhost:8080", "http://localhost:8081", "http://localhost", "http://127.0.0.1:3000"}, allowCredentials = "true")
public class TaskController {

    private static final Logger logger = LoggerFactory.getLogger(TaskController.class);

    @Autowired
    private OverdueDebtUpdateService overdueDebtUpdateService;

    /**
     * 手动触发逾期债权数据更新任务
     * 
     * @return 执行结果
     */
    @PostMapping("/trigger-debt-update")
    public ResponseEntity<Map<String, Object>> triggerDebtUpdate() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("收到手动触发逾期债权数据更新任务的请求");
            
            // 执行任务
            overdueDebtUpdateService.scheduleOverdueYearUpdate();
            
            response.put("success", true);
            response.put("message", "逾期债权数据更新任务执行成功");
            logger.info("逾期债权数据更新任务执行成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("执行逾期债权数据更新任务时发生错误", e);
            
            response.put("success", false);
            response.put("message", "执行任务时发生错误: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取任务状态信息
     * 
     * @return 任务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getTaskStatus() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("success", true);
        response.put("message", "定时任务已暂时禁用，可通过手动触发执行");
        response.put("scheduledTaskEnabled", false);
        response.put("manualTriggerAvailable", true);
        
        return ResponseEntity.ok(response);
    }
}
