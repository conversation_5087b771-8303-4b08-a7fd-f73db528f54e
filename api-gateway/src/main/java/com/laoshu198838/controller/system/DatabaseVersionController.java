package com.laoshu198838.controller.system;

import com.laoshu198838.service.DatabaseVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据库版本管理控制器
 * 提供数据库迁移和版本管理的API接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/admin/database")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class DatabaseVersionController {
    
    @Autowired
    private DatabaseVersionService databaseVersionService;
    
    /**
     * 获取数据库迁移信息
     */
    @GetMapping("/migration-info")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getMigrationInfo() {
        try {
            Map<String, Object> migrationInfo = databaseVersionService.getMigrationInfo();
            return ResponseEntity.ok(migrationInfo);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
    
    /**
     * 执行数据库迁移
     */
    @PostMapping("/migrate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> migrate() {
        Map<String, Object> result = databaseVersionService.migrate();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 验证数据库迁移
     */
    @PostMapping("/validate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> validate() {
        Map<String, Object> result = databaseVersionService.validate();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取当前数据库版本
     */
    @GetMapping("/version")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getCurrentVersion() {
        Map<String, Object> response = new HashMap<>();
        response.put("version", databaseVersionService.getCurrentVersion());
        response.put("hasPendingMigrations", databaseVersionService.hasPendingMigrations());
        return ResponseEntity.ok(response);
    }
    
    /**
     * 修复数据库迁移
     */
    @PostMapping("/repair")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> repair() {
        Map<String, Object> result = databaseVersionService.repair();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 获取数据库状态概览
     */
    @GetMapping("/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 获取基本信息
            status.put("currentVersion", databaseVersionService.getCurrentVersion());
            status.put("hasPendingMigrations", databaseVersionService.hasPendingMigrations());
            
            // 获取详细迁移信息
            Map<String, Object> migrationInfo = databaseVersionService.getMigrationInfo();
            status.put("totalMigrations", ((java.util.List<?>) migrationInfo.get("migrations")).size());
            status.put("appliedMigrations", migrationInfo.get("applied"));
            status.put("pendingMigrations", migrationInfo.get("pending"));
            
            // 验证状态
            Map<String, Object> validationResult = databaseVersionService.validate();
            status.put("isValid", validationResult.get("success"));
            
            status.put("success", true);
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }
}
