package com.laoshu198838.controller.monitoring;

import com.laoshu198838.model.datamonitor.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.laoshu198838.service.DebtManagementService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据监控控制器
 * 提供数据一致性检查和监控相关的API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/datamonitor")
public class DataMonitorController {

    private static final Logger logger = LoggerFactory.getLogger(DataMonitorController.class);

    private final DebtManagementService debtManagementService;

    @Autowired
    public DataMonitorController(DebtManagementService debtManagementService) {
        this.debtManagementService = debtManagementService;
    }

    /**
     * 检查新增表数据一致性
     *
     * 检查内容：
     * 1. 检查1-12月数据求和是否等于新增金额字段列的求和
     * 2. 检查处置金额是否等于现金处置+分期还款+资产抵债和其他方式的总和
     * 3. 检查新增金额列求和-处置金额列求和是否等于债权余额
     *
     * @param year 年份
     * @param month 月份
     * @return 新增表数据一致性检查结果
     */
    @GetMapping("/check-add-table-consistency")
    public ResponseEntity<AddTableConsistencyResult> checkAddTableConsistency(
            @RequestParam String year,
            @RequestParam String month) {

        try {
            logger.info("开始检查新增表数据一致性: 年份={}, 月份={}", year, month);
            AddTableConsistencyResult result = debtManagementService.checkAddTableConsistency(year, month);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("检查新增表数据一致性时发生错误: 年份={}, 月份={}", year, month, e);
            // 创建一个空结果对象，前端可以通过HTTP状态码判断错误
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new AddTableConsistencyResult());
        }
    }

    /**
     * 检查处置表数据一致性
     *
     * 检查内容：
     * 1. 检查处置表中的每月处置金额求和是否与现金处置、分期还款、资产抵债和其他方式求和相同
     *
     * @param year 年份
     * @param month 月份
     * @return 处置表数据一致性检查结果
     */
    @GetMapping("/check-disposal-table-consistency")
    public ResponseEntity<DisposalTableConsistencyResult> checkDisposalTableConsistency(
            @RequestParam String year,
            @RequestParam String month) {

        try {
            logger.info("开始检查处置表数据一致性: 年份={}, 月份={}", year, month);
            DisposalTableConsistencyResult result = debtManagementService.checkDisposalTableConsistency(year, month);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("检查处置表数据一致性时发生错误: 年份={}, 月份={}", year, month, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new DisposalTableConsistencyResult());
        }
    }

    /**
     * 检查债务变动一致性
     *
     * 检查内容：
     * 1. 检查新增表中的每月新增债务金额是否与诉讼表、非诉讼表和减值准备表中的新增债务金额一致
     * 2. 计算各表之间的差异并显示详细的对账信息
     *
     * @param year 年份
     * @param month 月份
     * @return 债务变动一致性检查结果
     */
    @GetMapping("/check-debt-change-consistency")
    public ResponseEntity<DebtChangeConsistencyResult> checkDebtChangeConsistency(
            @RequestParam String year,
            @RequestParam String month) {

        try {
            logger.info("开始检查债务变动一致性: 年份={}, 月份={}", year, month);
            DebtChangeConsistencyResult result = debtManagementService.checkDebtChangeConsistency(year, month);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("检查债务变动一致性时发生错误: 年份={}, 月份={}", year, month, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new DebtChangeConsistencyResult());
        }
    }

    /**
     * 获取数据总览
     *
     * 返回内容：
     * 1. 不同表之间的新增金额、处置金额和期末余额的汇总数据
     * 2. 数据以维度和表名为索引进行组织
     *
     * @param year 年份
     * @param month 月份
     * @return 数据总览结果
     */
    @GetMapping("/consistency/summary")
    public ResponseEntity<Map<String, Map<String, BigDecimal>>> getDataOverview(
            @RequestParam String year,
            @RequestParam String month) {

        try {
            logger.info("开始获取数据总览: 年份={}, 月份={}", year, month);
            DataOverviewResult result = debtManagementService.getDataOverview(year, month);
            return ResponseEntity.ok(result.getData());
        } catch (Exception e) {
            logger.error("获取数据总览时发生错误: 年份={}, 月份={}", year, month, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new HashMap<>());
        }
    }

    /**
     * 获取一致性检查明细
     *
     * 返回内容：
     * 1. 指定检查项的明细数据，只返回有差异的记录
     * 2. 支持对新增金额/处置金额/期末余额分别进行对账
     *
     * @param check 检查项 (newAmount, disposedAmount, endingBalance)
     * @param yearMonth 年月格式如 "2024-12"
     * @return 一致性检查明细列表
     */
    @GetMapping("/consistency/detail")
    public ResponseEntity<List<ConsistencyDetailItem>> getConsistencyDetail(
            @RequestParam String check,
            @RequestParam String yearMonth) {

        try {
            logger.info("开始获取一致性检查明细: 检查项={}, 年月={}", check, yearMonth);
            List<ConsistencyDetailItem> result = debtManagementService.getConsistencyDetail(check, yearMonth);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取一致性检查明细时发生错误: 检查项={}, 年月={}", check, yearMonth, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ArrayList<>());
        }
    }
}