package com.laoshu198838.controller.auth;

import com.laoshu198838.model.user.dto.LoginRequest;
import com.laoshu198838.model.user.dto.JwtResponse;
import com.laoshu198838.util.JwtUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Map;

/**
 * 认证控制器
 * <AUTHOR>
 */
@Tag(name = "认证管理", description = "用户认证相关API")
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    private static final String ERROR_KEY = "error";
    private static final String MESSAGE_KEY = "message";

    private final AuthenticationManager authenticationManager;
    private final JwtUtils jwtUtils;
    private final com.laoshu198838.service.UserSystemService userSystemService;

    public AuthController(AuthenticationManager authenticationManager, JwtUtils jwtUtils,
                         com.laoshu198838.service.UserSystemService userSystemService) {
        this.authenticationManager = authenticationManager;
        this.jwtUtils = jwtUtils;
        this.userSystemService = userSystemService;
    }

    @PostMapping("/auth/reset-password")
    public ResponseEntity<?> resetPassword(@RequestBody Map<String, String> request) {
        String username = request.get("username");
        String newPassword = request.get("newPassword");

        if (username == null || newPassword == null) {
            return ResponseEntity.badRequest()
                    .body(Collections.singletonMap(ERROR_KEY, "Username and newPassword are required"));
        }

        try {
            // 使用UserSystemService重置密码
            userSystemService.resetPassword(username, newPassword);
            logger.info("Password reset successful for user: {}", username);
            return ResponseEntity.ok(Collections.singletonMap("message", "Password reset successful"));
        } catch (Exception e) {
            logger.error("Password reset failed for user: {}", username, e);
            return ResponseEntity.status(500)
                    .body(Collections.singletonMap(ERROR_KEY, "Password reset failed: " + e.getMessage()));
        }
    }

    @Operation(summary = "用户登录", description = "用户使用用户名和密码进行登录认证")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登录成功",
                    content = @Content(schema = @Schema(implementation = JwtResponse.class))),
            @ApiResponse(responseCode = "401", description = "用户名或密码错误"),
            @ApiResponse(responseCode = "400", description = "请求参数无效"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/auth/login")
    public ResponseEntity<?> login(
            @Parameter(description = "登录请求信息", required = true)
            @RequestBody LoginRequest loginRequest) {
        logger.info("=== 收到登录请求 ===");
        logger.info("Request URI: /api/auth/login");
        logger.info("Request Method: POST");

        if (loginRequest == null || loginRequest.getUsername() == null || loginRequest.getPassword() == null) {
            logger.warn("Login attempt with invalid request data");
            return ResponseEntity.badRequest()
                    .body(Collections.singletonMap(ERROR_KEY, "Invalid request data"));
        }

        logger.info("=== 开始处理用户登录 ===");
        logger.info("Attempting login for user: {}", loginRequest.getUsername());
        try {
            // 创建认证令牌
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(),
                    loginRequest.getPassword()
            );

            // 尝试认证
            Authentication authentication = authenticationManager.authenticate(authToken);

            // 设置认证上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 生成JWT令牌
            String jwt = jwtUtils.generateToken(authentication);
            logger.info("Login successful for user: {}", loginRequest.getUsername());

            // 返回JWT响应
            return ResponseEntity.ok(new JwtResponse(jwt));

        } catch (BadCredentialsException e) {
            logger.warn("Login failed for user: {} - Invalid credentials", loginRequest.getUsername());
            return ResponseEntity.status(401)
                    .body(Collections.singletonMap(ERROR_KEY, "Invalid username or password"));

        } catch (Exception e) {
            logger.error("Unexpected error during login for user: {}", loginRequest.getUsername(), e);
            return ResponseEntity.status(500)
                    .body(Collections.singletonMap(ERROR_KEY, "An unexpected error occurred: " + e.getMessage()));
        }
    }

    @GetMapping("/protected")
    public ResponseEntity<Map<String, String>> getProtectedResource() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        logger.debug("Accessing protected resource by user: {}", username);

        return ResponseEntity.ok()
                .body(Collections.singletonMap(
                        MESSAGE_KEY,
                        "Hello " + username + ", this is a protected resource."
                ));
    }

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<Map<String, String>> handleBadCredentialsException(BadCredentialsException e) {
        logger.warn("Login failed: Invalid credentials", e);
        return ResponseEntity.status(401)
                .body(Collections.singletonMap(ERROR_KEY, "Invalid username or password"));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, String>> handleException(Exception e) {
        logger.error("Unexpected error during authentication", e);
        String errorMessage = e.getMessage() != null ? e.getMessage() : "An unexpected error occurred";
        return ResponseEntity.status(500)
                .body(Collections.singletonMap(ERROR_KEY, errorMessage));
    }
}
