package com.laoshu198838.controller.auth;

import com.laoshu198838.security.RoutePermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 权限验证控制器
 * 提供前端权限验证相关的API
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/permissions")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class PermissionController {
    
    @Autowired
    private RoutePermissionService routePermissionService;
    
    /**
     * 检查用户对特定路由的访问权限
     */
    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> checkPermission(
            @RequestParam String route,
            Authentication authentication) {
        
        Map<String, Object> response = new HashMap<>();
        
        boolean hasPermission = routePermissionService.hasPermission(authentication, route);
        
        response.put("hasPermission", hasPermission);
        response.put("route", route);
        response.put("authenticated", authentication != null && authentication.isAuthenticated());
        
        if (authentication != null && authentication.isAuthenticated()) {
            response.put("username", authentication.getName());
            response.put("authorities", authentication.getAuthorities());
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取用户可访问的所有路由
     */
    @GetMapping("/accessible-routes")
    public ResponseEntity<Map<String, Object>> getAccessibleRoutes(Authentication authentication) {
        
        Map<String, Object> response = new HashMap<>();
        
        Set<String> accessibleRoutes = routePermissionService.getAccessibleRoutes(authentication);
        
        response.put("accessibleRoutes", accessibleRoutes);
        response.put("authenticated", authentication != null && authentication.isAuthenticated());
        
        if (authentication != null && authentication.isAuthenticated()) {
            response.put("username", authentication.getName());
            response.put("isAdmin", routePermissionService.isAdmin(authentication));
            response.put("hasExportPermission", routePermissionService.hasExportPermission(authentication));
            response.put("hasAuditPermission", routePermissionService.hasAuditPermission(authentication));
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取用户权限信息
     */
    @GetMapping("/user-permissions")
    public ResponseEntity<Map<String, Object>> getUserPermissions(Authentication authentication) {
        
        Map<String, Object> response = new HashMap<>();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            response.put("authenticated", false);
            response.put("permissions", Map.of());
            return ResponseEntity.ok(response);
        }
        
        Map<String, Boolean> permissions = new HashMap<>();
        permissions.put("isAdmin", routePermissionService.isAdmin(authentication));
        permissions.put("hasExportPermission", routePermissionService.hasExportPermission(authentication));
        permissions.put("hasAuditPermission", routePermissionService.hasAuditPermission(authentication));
        
        response.put("authenticated", true);
        response.put("username", authentication.getName());
        response.put("authorities", authentication.getAuthorities());
        response.put("permissions", permissions);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 批量检查路由权限
     */
    @PostMapping("/check-batch")
    public ResponseEntity<Map<String, Object>> checkBatchPermissions(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        @SuppressWarnings("unchecked")
        java.util.List<String> routes = (java.util.List<String>) request.get("routes");
        
        Map<String, Object> response = new HashMap<>();
        Map<String, Boolean> routePermissions = new HashMap<>();
        
        if (routes != null) {
            for (String route : routes) {
                boolean hasPermission = routePermissionService.hasPermission(authentication, route);
                routePermissions.put(route, hasPermission);
            }
        }
        
        response.put("routePermissions", routePermissions);
        response.put("authenticated", authentication != null && authentication.isAuthenticated());
        
        return ResponseEntity.ok(response);
    }
}
