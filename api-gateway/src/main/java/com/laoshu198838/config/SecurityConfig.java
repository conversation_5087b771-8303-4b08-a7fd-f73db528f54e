package com.laoshu198838.config;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.laoshu198838.filter.JwtAuthenticationFilter;
import com.laoshu198838.security.RoutePermissionService;
import com.laoshu198838.service.UserSystemDetailsService;

/**
 * 安全配置类
 * Spring Security的核心配置，处理认证、授权和安全相关设置
 * 
 * 功能特性：
 * - JWT认证：无状态会话，使用JWT令牌进行用户认证
 * - 路径权限控制：基于角色的访问控制（RBAC）
 * - CORS配置：跨域资源共享设置
 * - 密码加密：使用BCrypt算法加密用户密码
 * - 多数据源认证：支持user_system数据库用户认证
 * 
 * 使用情况：
 * - AuthController: 使用authenticationManager进行用户认证
 * - JwtAuthenticationFilter: 与JWT过滤器配合使用
 * - 所有HTTP请求: 通过securityFilterChain进行安全检查
 * - WebConfig: 与Web配置的CORS设置协同工作
 * 
 * 权限级别：
 * - 公开访问：/api/auth/login, /api/public/**, /swagger-ui/**
 * - 认证用户：/api/oa/**, /api/permissions/**
 * - 普通用户：/api/debts/**, /api/assets/**, /api/reports/**
 * - 管理员：/api/users/**, /api/admin/**, /actuator/**
 * - 审计员：/api/audit/**
 * 
 * <AUTHOR>
 */
@Configuration
public class SecurityConfig {

    private static final Logger logger = LoggerFactory.getLogger(SecurityConfig.class);

    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Autowired
    private UserSystemDetailsService userSystemDetailsService;

    public SecurityConfig(JwtAuthenticationFilter jwtAuthenticationFilter) {
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
    }

    /**
     * 配置 HTTP 安全性，定义哪些请求需要认证，哪些请求可以公开访问。
     *
     * @param http HttpSecurity 对象，用于配置 HTTP 安全性
     * @return 配置好的 SecurityFilterChain
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        logger.info("初始化安全过滤器链");
        http
                // 启用 CORS 配置 - 使用默认配置，由WebConfig处理
                .cors(Customizer.withDefaults())
                // 禁用 CSRF
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(authorize -> {
                    authorize
                                               // 公共路径 - 无需认证
                                               .requestMatchers("/api/auth/login", "/api/users/register", "/api/public/**").permitAll()

                                               // OA工作流管理路径 - 认证用户即可访问（优先匹配）
                                               .requestMatchers("/api/oa/**").authenticated()

                                               // 测试导出接口 - 无需认证（仅用于调试）
                                               .requestMatchers("/api/export/test/**").permitAll()

                                               // Swagger文档路径 - 无需认证
                                               .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html").permitAll()

                                               // 监控端点 - 部分公开，部分需要管理员权限
                                               .requestMatchers("/actuator/health", "/actuator/info", "/actuator/prometheus").permitAll()
                                               .requestMatchers("/actuator/**").hasRole("ADMIN")

                                               // 权限验证API - 需要认证
                                               .requestMatchers("/api/permissions/**").authenticated()

                                               // 管理员专用路径
                                               .requestMatchers("/api/users/**", "/api/datamonitor/**", "/api/consistency/**", "/api/admin/**").hasAnyAuthority("ADMIN", "ROLE_ADMIN")

                                               // 审计相关路径
                                               .requestMatchers("/api/audit/**").hasAnyAuthority("ADMIN", "AUDITOR", "ROLE_ADMIN", "ROLE_AUDITOR")

                                               // 数据导出路径
                                               .requestMatchers("/api/export/**").hasAnyAuthority("ADMIN", "EXPORT_USER", "ROLE_ADMIN", "ROLE_EXPORT_USER")

                                               // 普通用户路径
                                               .requestMatchers("/api/debts/**", "/api/assets/**", "/api/reports/**", "/api/profile/**")
                                               .hasAnyAuthority("USER", "ADMIN", "ROLE_USER", "ROLE_ADMIN")

                                               // 其他所有API路径需要认证
                                               .requestMatchers("/api/**").authenticated()

                                               // 其他所有路径需要认证
                                               .anyRequest().authenticated();
                                      })
                // 配置无状态会话：禁用 HTTP session，适合 JWT 认证
                .sessionManagement(sessionManagement -> {
                    sessionManagement.sessionCreationPolicy(SessionCreationPolicy.STATELESS);
                })
                // 添加自定义的 JWT 认证过滤器到 Spring Security 的过滤器链中
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        logger.info("安全配置初始化完成");
        return http.build();
    }

    /**
     * 配置密码加密器，使用 BCrypt 算法来加密和验证用户密码。
     *
     * @return BCryptPasswordEncoder 实例，用于密码加密
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        // 返回一个新的 BCryptPasswordEncoder 实例
        return new BCryptPasswordEncoder();
    }

    /**
     * 配置数据库认证提供者
     * 使用UserSystemDetailsService从user_system数据库进行用户认证
     */
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(userSystemDetailsService);
        provider.setPasswordEncoder(passwordEncoder());
        return provider;
    }

    /**
     * 创建并暴露 AuthenticationManager
     * 使用user_system数据库进行用户认证
     *
     * @param http HttpSecurity 对象，用于配置 HTTP 安全性
     * @return AuthenticationManager 实例
     * @throws Exception 配置异常
     */
    @Bean
    public AuthenticationManager authenticationManager(HttpSecurity http) throws Exception {
        AuthenticationManagerBuilder authenticationManagerBuilder =
                http.getSharedObject(AuthenticationManagerBuilder.class);

        // 配置数据库认证提供者
        authenticationManagerBuilder.authenticationProvider(daoAuthenticationProvider());

        return authenticationManagerBuilder.build();
    }


}
