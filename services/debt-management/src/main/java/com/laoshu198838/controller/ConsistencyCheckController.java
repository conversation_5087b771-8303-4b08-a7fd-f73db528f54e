package com.laoshu198838.controller;

import com.laoshu198838.service.ConsistencyCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;

/**
 * 数据一致性检查控制器
 * 提供数据一致性检查相关的API接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/consistency")
@CrossOrigin(origins = "*")
public class ConsistencyCheckController {

    private static final Logger logger = LoggerFactory.getLogger(ConsistencyCheckController.class);

    @Autowired
    private ConsistencyCheckService consistencyCheckService;

    /**
     * 执行完整的数据一致性检查
     * 
     * @param year 年份（可选，默认当前年份）
     * @param month 月份（可选，默认当前月份）
     * @return 一致性检查结果
     */
    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> performConsistencyCheck(
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer month) {
        
        try {
            // 如果没有指定年月，使用当前年月
            LocalDate now = LocalDate.now();
            int checkYear = year != null ? year : now.getYear();
            int checkMonth = month != null ? month : now.getMonthValue();
            
            logger.info("执行数据一致性检查，年份: {}, 月份: {}", checkYear, checkMonth);
            
            Map<String, Object> result = consistencyCheckService.performFullConsistencyCheck(checkYear, checkMonth);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("数据一致性检查失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取一致性检查报告
     * 
     * @param year 年份（可选，默认当前年份）
     * @param month 月份（可选，默认当前月份）
     * @return 格式化的检查报告
     */
    @GetMapping("/report")
    public ResponseEntity<Map<String, Object>> getConsistencyReport(
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer month) {
        
        try {
            // 如果没有指定年月，使用当前年月
            LocalDate now = LocalDate.now();
            int checkYear = year != null ? year : now.getYear();
            int checkMonth = month != null ? month : now.getMonthValue();
            
            logger.info("获取数据一致性检查报告，年份: {}, 月份: {}", checkYear, checkMonth);
            
            Map<String, Object> report = consistencyCheckService.getConsistencyReport(checkYear, checkMonth);
            
            return ResponseEntity.ok(report);
            
        } catch (Exception e) {
            logger.error("获取一致性检查报告失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = Map.of(
                "status", "UP",
                "service", "ConsistencyCheckService",
                "timestamp", System.currentTimeMillis()
            );
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
