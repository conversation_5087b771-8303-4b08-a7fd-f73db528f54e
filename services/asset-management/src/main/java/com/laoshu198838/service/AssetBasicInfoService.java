package com.laoshu198838.service;

import com.laoshu198838.dto.asset.AssetBasicInfoDTO;
import com.laoshu198838.entity.asset.AssetBasicInfo;
import com.laoshu198838.repository.asset.AssetBasicInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 资产基本信息服务类
 * 
 * <AUTHOR>
 */
@Service
@Transactional
public class AssetBasicInfoService {

    private static final Logger logger = LoggerFactory.getLogger(AssetBasicInfoService.class);

    @Autowired
    private AssetBasicInfoRepository assetBasicInfoRepository;

    /**
     * 创建资产
     */
    public AssetBasicInfoDTO createAsset(AssetBasicInfoDTO dto, String currentUser) {
        logger.info("创建资产: {}, 操作人: {}", dto.getAssetName(), currentUser);
        
        // 验证资产名称是否重复
        if (dto.getId() == null && assetBasicInfoRepository.existsByAssetNameAndManagementCompanyAndIdNot(
                dto.getAssetName(), dto.getManagementCompany(), 0L)) {
            throw new RuntimeException("资产名称已存在: " + dto.getAssetName());
        }
        
        // 验证购买合同编号是否重复
        if (dto.getPurchaseContractNo() != null && 
            assetBasicInfoRepository.existsByPurchaseContractNoAndManagementCompanyAndIdNot(
                dto.getPurchaseContractNo(), dto.getManagementCompany(), 0L)) {
            throw new RuntimeException("购买合同编号已存在: " + dto.getPurchaseContractNo());
        }

        AssetBasicInfo entity = dto.toEntity();
        entity.setCreatedBy(currentUser);
        entity.setUpdatedBy(currentUser);
        
        AssetBasicInfo saved = assetBasicInfoRepository.save(entity);
        logger.info("资产创建成功，ID: {}", saved.getId());
        
        return AssetBasicInfoDTO.fromEntity(saved);
    }

    /**
     * 更新资产
     */
    public AssetBasicInfoDTO updateAsset(Long id, AssetBasicInfoDTO dto, String currentUser) {
        logger.info("更新资产: {}, 操作人: {}", id, currentUser);
        
        Optional<AssetBasicInfo> existingOpt = assetBasicInfoRepository.findById(id);
        if (existingOpt.isEmpty()) {
            throw new RuntimeException("资产不存在，ID: " + id);
        }
        
        // 验证资产名称是否重复
        if (assetBasicInfoRepository.existsByAssetNameAndManagementCompanyAndIdNot(
                dto.getAssetName(), dto.getManagementCompany(), id)) {
            throw new RuntimeException("资产名称已存在: " + dto.getAssetName());
        }
        
        // 验证购买合同编号是否重复
        if (dto.getPurchaseContractNo() != null && 
            assetBasicInfoRepository.existsByPurchaseContractNoAndManagementCompanyAndIdNot(
                dto.getPurchaseContractNo(), dto.getManagementCompany(), id)) {
            throw new RuntimeException("购买合同编号已存在: " + dto.getPurchaseContractNo());
        }

        AssetBasicInfo entity = dto.toEntity();
        entity.setId(id);
        entity.setUpdatedBy(currentUser);
        
        AssetBasicInfo saved = assetBasicInfoRepository.save(entity);
        logger.info("资产更新成功，ID: {}", saved.getId());
        
        return AssetBasicInfoDTO.fromEntity(saved);
    }

    /**
     * 删除资产
     */
    public void deleteAsset(Long id, String currentUser) {
        logger.info("删除资产: {}, 操作人: {}", id, currentUser);
        
        Optional<AssetBasicInfo> existingOpt = assetBasicInfoRepository.findById(id);
        if (existingOpt.isEmpty()) {
            throw new RuntimeException("资产不存在，ID: " + id);
        }
        
        // 软删除：设置状态为已处置
        AssetBasicInfo entity = existingOpt.get();
        entity.setStatus(AssetBasicInfo.AssetStatus.DISPOSED);
        entity.setUpdatedBy(currentUser);
        
        assetBasicInfoRepository.save(entity);
        logger.info("资产删除成功，ID: {}", id);
    }

    /**
     * 根据ID查询资产
     */
    @Transactional(readOnly = true)
    public AssetBasicInfoDTO getAssetById(Long id) {
        Optional<AssetBasicInfo> entityOpt = assetBasicInfoRepository.findById(id);
        return entityOpt.map(AssetBasicInfoDTO::fromEntity).orElse(null);
    }

    /**
     * 根据管理公司查询资产列表
     */
    @Transactional(readOnly = true)
    public List<AssetBasicInfoDTO> getAssetsByManagementCompany(String managementCompany) {
        List<AssetBasicInfo> entities = assetBasicInfoRepository.findByManagementCompanyAndStatus(
            managementCompany, AssetBasicInfo.AssetStatus.ACTIVE);
        return entities.stream()
                .map(AssetBasicInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 分页查询资产列表
     */
    @Transactional(readOnly = true)
    public Page<AssetBasicInfoDTO> getAssetsByPage(String managementCompany, Pageable pageable) {
        Page<AssetBasicInfo> entities = assetBasicInfoRepository.findByManagementCompanyAndStatus(
            managementCompany, AssetBasicInfo.AssetStatus.ACTIVE, pageable);
        return entities.map(AssetBasicInfoDTO::fromEntity);
    }

    /**
     * 根据条件查询资产
     */
    @Transactional(readOnly = true)
    public Page<AssetBasicInfoDTO> getAssetsByConditions(
            String managementCompany, 
            AssetBasicInfo.AssetType assetType,
            Long propertyOwnerId,
            Boolean hasPropertyCertificate,
            Pageable pageable) {
        
        Page<AssetBasicInfo> entities = assetBasicInfoRepository.findByConditions(
            managementCompany, assetType, propertyOwnerId, hasPropertyCertificate, 
            AssetBasicInfo.AssetStatus.ACTIVE, pageable);
        return entities.map(AssetBasicInfoDTO::fromEntity);
    }

    /**
     * 查询瑕疵资产
     */
    @Transactional(readOnly = true)
    public List<AssetBasicInfoDTO> getDefectiveAssets(String managementCompany) {
        List<AssetBasicInfo> entities = assetBasicInfoRepository.findByHasPropertyCertificateAndManagementCompanyAndStatus(
            false, managementCompany, AssetBasicInfo.AssetStatus.ACTIVE);
        return entities.stream()
                .map(AssetBasicInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取资产类型统计
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getAssetTypeStatistics(String managementCompany) {
        List<Object[]> results = assetBasicInfoRepository.getAssetTypeStatistics(
            managementCompany, AssetBasicInfo.AssetStatus.ACTIVE);
        
        return results.stream().collect(Collectors.toMap(
            row -> ((AssetBasicInfo.AssetType) row[0]).getDescription(),
            row -> Map.of(
                "count", row[1],
                "totalArea", row[2]
            )
        ));
    }

    /**
     * 获取瑕疵资产统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getDefectiveAssetStatistics() {
        List<Object[]> results = assetBasicInfoRepository.getDefectiveAssetStatistics(
            AssetBasicInfo.AssetStatus.ACTIVE);
        
        return results.stream().map(row -> Map.of(
            "managementCompany", row[0],
            "assetType", ((AssetBasicInfo.AssetType) row[1]).getDescription(),
            "count", row[2],
            "totalArea", row[3]
        )).collect(Collectors.toList());
    }

    /**
     * 获取管理公司列表
     */
    @Transactional(readOnly = true)
    public List<String> getManagementCompanies() {
        return assetBasicInfoRepository.findDistinctManagementCompanies(AssetBasicInfo.AssetStatus.ACTIVE);
    }

    /**
     * 获取资产总面积
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalAreaByManagementCompany(String managementCompany) {
        BigDecimal totalArea = assetBasicInfoRepository.getTotalAreaByManagementCompany(
            managementCompany, AssetBasicInfo.AssetStatus.ACTIVE);
        return totalArea != null ? totalArea : BigDecimal.ZERO;
    }
}
