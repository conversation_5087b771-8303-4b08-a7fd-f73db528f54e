{"permissions": {"allow": ["Bash(npm start)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(npm run build:*)", "Bash(npm run format:*)", "Bash(npm run lint:*)", "Bash(npm run quality:fix:*)", "Bash(SKIP_PREFLIGHT_CHECK=true npm start)", "Bash(chromedriver:*)", "Bash(/Applications/Google Chrome.app/Contents/MacOS/Google Chrome --version)", "<PERSON><PERSON>(mvn clean:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(pkill:*)", "mcp__filesystem__list_directory", "mcp__filesystem__list_allowed_directories", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(mvn:*)", "Bash(npm run quality:check:*)", "Bash(ESLINT_NO_DEV_ERRORS=true npm run build)", "Bash(DISABLE_ESLINT_PLUGIN=true npm run build)", "Bash(timeout 30 mvn -f ../api-gateway/pom.xml spring-boot:run)", "<PERSON><PERSON>(npx prettier:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm install:*)", "Bash(rm:*)", "Bash(npm cache clean:*)", "Bash(yarn install)", "Bash(yarn start)", "Bash(grep:*)", "Bash(ping:*)", "<PERSON><PERSON>(sudo launchctl:*)", "<PERSON><PERSON>(launchctl:*)", "Bash(sudo pfctl:*)", "Bash(tailscale:*)", "Bash(ssh:*)", "Bash(traceroute:*)", "<PERSON><PERSON>(cat:*)", "Bash(/Applications/Tailscale.app/Contents/MacOS/Tailscale status)", "Bash(/Applications/Tailscale.app/Contents/MacOS/Tailscale ping 100.127.204.72)", "Bash(nc:*)", "Bash(expect:*)"], "deny": []}}