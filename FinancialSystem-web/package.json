{"name": "financial-system", "version": "2.2.0", "private": true, "author": "Creative Tim", "license": "See license in https://www.creative-tim.com/license", "description": "Financial System", "bugs": {"url": "https://github.com/creativetimofficial/material-dashboard-react/issues"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/material-dashboard-react.git"}, "proxy": "http://localhost:8080", "dependencies": {"@emotion/cache": "11.10.8", "@emotion/react": "11.10.8", "@emotion/styled": "11.10.8", "@mui/icons-material": "5.11.16", "@mui/material": "^5.15.20", "@mui/system": "^5.15.20", "@mui/x-date-pickers": "^7.29.4", "axios": "^1.7.9", "chart.js": "4.3.0", "chartjs-plugin-datalabels": "^2.2.0", "chroma-js": "2.4.2", "date-fns": "^2.30.0", "eslint": "^9.20.1", "handsontable": "^13.1.0", "http-proxy-middleware": "^3.0.5", "jwt-decode": "^4.0.0", "prop-types": "15.8.1", "react": "^18.2.0", "react-chartjs-2": "5.2.0", "react-dom": "^18.2.0", "react-router-dom": "6.11.0", "react-table": "7.8.0", "recharts": "^2.15.1", "stylis": "4.1.4", "stylis-plugin-rtl": "^1.1.0", "xlsx": "^0.18.5"}, "scripts": {"dev": "react-scripts start", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint src", "lint:fix": "eslint src --fix", "type:check": "tsc --noEmit", "style:lint": "stylelint 'src/**/*.{css,scss}'", "style:lint:fix": "stylelint 'src/**/*.{css,scss}' --fix", "test:coverage": "react-scripts test --coverage --silent --watchAll=false", "quality:check": "npm run format:check && npm run lint && npm run type:check", "quality:fix": "npm run format && npm run lint:fix && npm run style:lint:fix", "pre-commit": "npm run quality:fix && npm run test:coverage && npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@types/chroma-js": "^2.4.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "@types/react-table": "^7.7.14", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint-config-prettier": "8.8.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "^7.32.2", "prettier": "^2.8.8", "react-scripts": "^5.0.1", "typescript": "^5.8.3"}, "overrides": {"svgo": "3.0.2"}}