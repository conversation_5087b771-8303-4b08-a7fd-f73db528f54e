# 🏦 司库系统数据提取指南

## 📋 概述

本文档详细说明了从中信银行天元司库系统可以提取的所有数据类型和字段信息，为财务系统的数据集成提供完整的参考。

## 🎯 可提取的核心数据类型

### 1. 💰 账户余额数据

**接口代码**: `SKBALQRY`
**功能**: 查询司库中活期账户的实时余额信息

#### 可获取字段

| 字段名        | 数据类型      | 说明                                        |
| ------------- | ------------- | ------------------------------------------- |
| accountNo     | varchar(40)   | 账户号码                                    |
| accountName   | varchar(120)  | 账户名称                                    |
| usableBalance | decimal(15,2) | **可用账户余额** - 可操作的账户余额         |
| balance       | decimal(15,2) | **账户总余额** - 包含冻结金额、可操作余额等 |
| fraAmt        | decimal(15,2) | **冻结余额** - 账户冻结金额                 |
| lastUdtTms    | char(15)      | 余额更新时间                                |
| dataSrc       | varchar(16)   | 数据来源（直联、非直联-人工等）             |
| currencyID    | varchar(5)    | 币种类型                                    |
| date          | char(8)       | 余额更新日期                                |

#### 业务价值

- ✅ **实时资金监控** - 掌握各账户实时资金状况
- ✅ **资金可用性分析** - 区分可用余额和冻结余额
- ✅ **多币种管理** - 支持不同币种账户
- ✅ **数据时效性** - 获取最新的余额更新时间

### 2. 📊 当日交易明细数据

**接口代码**: `SKTRNCOL`
**功能**: 查询账户的当日交易明细信息

#### 可获取字段

| 字段名          | 数据类型      | 说明                                    |
| --------------- | ------------- | --------------------------------------- |
| sumTranNo       | varchar(20)   | **系统交易流水号** - 司库系统内唯一标识 |
| tranDate        | char(8)       | **交易日期**                            |
| tranTime        | char(6)       | **交易时间**                            |
| accountingDate  | char(8)       | **记账日期** - 银行起息记账日期         |
| accountNo       | varchar(32)   | **本方账号**                            |
| openBankName    | varchar(300)  | **本方开户行**                          |
| accountName     | varchar(120)  | **本方户名**                            |
| bankName        | varchar(120)  | **本方所属银行**                        |
| instName        | varchar(360)  | **机构名称**                            |
| oppAccountNo    | varchar(40)   | **对方账号**                            |
| oppAccountName  | varchar(120)  | **对方账户名称**                        |
| oppOpenBankName | varchar(120)  | **对方开户行名**                        |
| oppOpenBankNo   | varchar(32)   | **对方开户行联行号**                    |
| tranAmt         | decimal(15,2) | **交易金额**                            |
| balance         | decimal(15,2) | **交易后余额**                          |
| tranType        | varchar(2)    | **交易类型** (借贷方向)                 |
| currencyID      | varchar(5)    | **币种**                                |
| summary         | varchar(200)  | **摘要信息**                            |
| purpose         | varchar(200)  | **用途**                                |
| remark          | varchar(500)  | **备注**                                |

#### 业务价值

- ✅ **交易对方信息** - 完整的收付款方信息
- ✅ **资金流向分析** - 详细的收支明细
- ✅ **交易时间追踪** - 精确到秒的交易时间
- ✅ **余额变动监控** - 每笔交易后的余额状态

### 3. 📈 历史交易明细数据

**接口代码**: `SKHISQRY` (申请) + `SKHISRES` (结果查询)
**功能**: 查询指定时间范围内的历史交易明细

#### 可获取字段

| 字段名          | 数据类型      | 说明                          |
| --------------- | ------------- | ----------------------------- |
| tranDate        | char(8)       | **交易日期**                  |
| tranTime        | char(6)       | **交易时间**                  |
| accountingDate  | char(8)       | **记账日期**                  |
| sumTranNo       | varchar(20)   | **系统交易流水号**            |
| accountNo       | varchar(32)   | **本方账号**                  |
| accountName     | varchar(120)  | **本方户名**                  |
| oppAccountNo    | varchar(40)   | **对方账号**                  |
| oppAccountName  | varchar(120)  | **对方账户名称**              |
| oppOpenBankName | varchar(120)  | **对方开户行**                |
| tranAmt         | decimal(15,2) | **交易金额**                  |
| balance         | decimal(15,2) | **交易后余额**                |
| dcFlag          | varchar(1)    | **借贷标志** (D-借方, C-贷方) |
| currencyID      | varchar(5)    | **币种**                      |
| summary         | varchar(200)  | **摘要**                      |
| purpose         | varchar(200)  | **用途**                      |
| remark          | varchar(500)  | **备注**                      |
| refundFlag      | varchar(1)    | **退汇标识**                  |
| tranSeqNo       | varchar(20)   | **交易流水号**                |

#### 业务价值

- ✅ **历史数据分析** - 长期交易趋势分析
- ✅ **对账功能** - 与内部系统进行对账
- ✅ **审计支持** - 提供完整的交易历史记录

### 4. 📋 账户信息数据

**接口代码**: `SKACCQRY`
**功能**: 查询账户的基本信息和权限

#### 可获取字段

| 字段名        | 数据类型     | 说明                 |
| ------------- | ------------ | -------------------- |
| accountNo     | varchar(40)  | **账户号码**         |
| accountName   | varchar(120) | **账户名称**         |
| openBankName  | varchar(300) | **开户行名称**       |
| bankName      | varchar(120) | **所属银行**         |
| currencyID    | varchar(5)   | **币种**             |
| accountType   | varchar(2)   | **账户类型**         |
| accountStatus | varchar(2)   | **账户状态**         |
| hasPermission | varchar(1)   | **是否具有使用权限** |
| instCode      | varchar(20)  | **机构编码**         |
| instName      | varchar(360) | **机构名称**         |

#### 业务价值

- ✅ **账户管理** - 完整的账户基础信息
- ✅ **权限控制** - 确认账户使用权限
- ✅ **机构关联** - 账户与机构的对应关系

### 5. 💹 历史余额数据

**接口代码**: `SKHISBAL` (申请) + `SKHISBALRES` (结果查询)
**功能**: 查询账户的历史余额信息

#### 可获取字段

| 字段名        | 数据类型      | 说明         |
| ------------- | ------------- | ------------ |
| accountNo     | varchar(40)   | **账户号码** |
| accountName   | varchar(120)  | **账户名称** |
| date          | char(8)       | **余额日期** |
| balance       | decimal(15,2) | **账户余额** |
| usableBalance | decimal(15,2) | **可用余额** |
| fraAmt        | decimal(15,2) | **冻结金额** |
| currencyID    | varchar(5)    | **币种**     |

#### 业务价值

- ✅ **资金趋势分析** - 账户余额历史变化
- ✅ **资金规划** - 基于历史数据的资金预测
- ✅ **合规报告** - 满足监管要求的历史数据

## 🔧 系统配置信息

### 账户配置

| 账户类型   | 账户号码            | 账户名称                     | 开户网点             |
| ---------- | ------------------- | ---------------------------- | -------------------- |
| 付款账号 1 | 8110701012901269085 | 深圳万润科技股份有限公司 ERP | 中信银行北京朝阳支行 |
| 付款账号 2 | 8110701013101269086 | 深圳万润科技股份有限公司 ERP | 中信银行北京朝阳支行 |
| 付款账号 3 | 8110701013501269087 | 深圳万润科技股份有限公司 ERP | 中信银行北京朝阳支行 |

### 测试账户

| 账户类型     | 账户号码            | 账户名称         | 开户网点               |
| ------------ | ------------------- | ---------------- | ---------------------- |
| 测试收款对公 | 8110701013501262521 | 培训机构 70      | 中信银行北京分行营业部 |
| 测试收款对私 | 8110701213401262624 | 收款对私测试账户 | 中信银行北京分行营业部 |

### 连接配置

| 参数         | 值           | 说明             |
| ------------ | ------------ | ---------------- |
| 客户号       | 001701393763 | 司库系统客户标识 |
| 直联用户代码 | SZWR003_ZL   | 直联接口用户代码 |
| 直联端口     | 6767         | 司库系统接口端口 |
| 用户名       | ***********  | 登录用户名       |

## 📊 数据提取策略

### 实时数据提取

- **账户余额** - 每 5 分钟自动查询一次
- **当日交易明细** - 每 30 分钟增量查询
- **账户信息** - 每日查询一次更新

### 历史数据提取

- **历史交易明细** - 按月批量提取
- **历史余额** - 每日定时提取前一日数据
- **对账数据** - 每日提取用于系统对账

### 数据存储建议

```sql
-- 账户余额表
CREATE TABLE treasury_account_balance (
    account_no VARCHAR(40),
    account_name VARCHAR(120),
    usable_balance DECIMAL(15,2),
    total_balance DECIMAL(15,2),
    frozen_amount DECIMAL(15,2),
    currency_id VARCHAR(5),
    update_time DATETIME,
    data_date DATE
);

-- 交易明细表
CREATE TABLE treasury_transaction_detail (
    sum_tran_no VARCHAR(20) PRIMARY KEY,
    tran_date DATE,
    tran_time TIME,
    accounting_date DATE,
    account_no VARCHAR(32),
    account_name VARCHAR(120),
    opp_account_no VARCHAR(40),
    opp_account_name VARCHAR(120),
    opp_open_bank_name VARCHAR(120),
    tran_amt DECIMAL(15,2),
    balance DECIMAL(15,2),
    dc_flag VARCHAR(1),
    currency_id VARCHAR(5),
    summary VARCHAR(200),
    purpose VARCHAR(200),
    remark VARCHAR(500)
);
```

## 🎯 业务应用场景

### 1. 资金管理

- **实时资金监控** - 监控各账户资金状况
- **资金调度** - 基于余额信息进行资金调配
- **流动性管理** - 分析资金流入流出情况

### 2. 财务分析

- **收支分析** - 基于交易明细分析收支结构
- **对方分析** - 分析主要交易对手方
- **趋势分析** - 基于历史数据分析资金趋势

### 3. 风险控制

- **异常交易监控** - 监控大额或异常交易
- **余额预警** - 设置余额阈值预警
- **对账管理** - 与内部系统进行自动对账

### 4. 合规报告

- **监管报告** - 生成监管要求的资金报告
- **审计支持** - 提供完整的交易审计轨迹
- **内控管理** - 支持内部控制制度执行

## 🔍 总结

通过司库系统接口，您可以获取到：

✅ **完整的账户信息** - 账户基本信息、余额、权限等
✅ **详细的交易数据** - 包含交易对方、金额、时间等完整信息
✅ **历史数据支持** - 支持历史交易和余额查询
✅ **实时数据更新** - 获取最新的账户和交易状态
✅ **多维度分析** - 支持按时间、账户、交易类型等多维度分析

这些数据完全可以满足财务系统的资金管理、风险控制、合规报告等各种业务需求。

## 📋 接口调用示例

### 1. 余额查询示例

```xml
<!-- 请求报文 -->
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBALQRY</action>
    <userName>***********</userName>
    <list name="userDataList">
        <row>
            <accountNo>8110701012901269085</accountNo>
        </row>
    </list>
</stream>

<!-- 响应报文 -->
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status>SUCCESS</status>
    <statusText>交易成功</statusText>
    <list name="userDataList">
        <row>
            <accountNo>8110701012901269085</accountNo>
            <accountName>深圳万润科技股份有限公司ERP</accountName>
            <usableBalance>1000000.00</usableBalance>
            <balance>1000000.00</balance>
            <fraAmt>0.00</fraAmt>
            <lastUdtTms>**************</lastUdtTms>
            <dataSrc>直联</dataSrc>
            <currencyID>CNY</currencyID>
            <date>********</date>
        </row>
    </list>
</stream>
```

### 2. 当日交易明细查询示例

```xml
<!-- 请求报文 -->
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKTRNCOL</action>
    <userName>***********</userName>
    <tranType>01</tranType>
    <startRecord>1</startRecord>
    <pageNumber>100</pageNumber>
    <list name="userDataList">
        <row>
            <accountNo>8110701012901269085</accountNo>
        </row>
    </list>
</stream>
```

## 🔧 技术实现要点

### 字符编码

- **请求编码**: GBK
- **响应编码**: GBK
- **系统内部**: UTF-8 (需要编码转换)

### 分页查询

- **起始记录号**: 从 1 开始
- **每页最大记录数**: 100 条
- **总记录数**: 响应中返回 totalRecords 字段

### 错误处理

| 状态码  | 说明     | 处理方式           |
| ------- | -------- | ------------------ |
| SUCCESS | 交易成功 | 正常处理数据       |
| FAILURE | 交易失败 | 检查请求参数和权限 |
| TIMEOUT | 超时     | 重试机制           |

### 数据同步策略

1. **实时同步**: 余额查询、当日交易
2. **定时同步**: 历史数据、账户信息
3. **增量同步**: 基于时间戳的增量更新
4. **全量同步**: 定期全量数据校验

## 📈 数据分析维度

### 按时间维度

- **日度分析**: 每日资金变化
- **月度分析**: 月度收支统计
- **年度分析**: 年度资金趋势

### 按账户维度

- **单账户分析**: 特定账户的资金状况
- **多账户对比**: 不同账户的资金分布
- **账户组分析**: 按业务分组的账户分析

### 按交易对方维度

- **主要客户分析**: 重要交易对手方
- **银行分析**: 不同银行的交易分布
- **地区分析**: 按地区统计的资金流向

### 按金额维度

- **大额交易**: 超过阈值的重要交易
- **小额交易**: 日常运营交易
- **异常交易**: 偏离正常模式的交易

## 🚀 系统集成建议

### 数据库设计

```sql
-- 扩展交易明细表，增加分析字段
ALTER TABLE treasury_transaction_detail ADD COLUMN (
    tran_type_desc VARCHAR(50),     -- 交易类型描述
    amount_level VARCHAR(20),       -- 金额级别(大额/中额/小额)
    business_type VARCHAR(50),      -- 业务类型
    risk_level VARCHAR(20),         -- 风险级别
    sync_time DATETIME,             -- 同步时间
    data_source VARCHAR(20)         -- 数据来源
);

-- 创建账户分析视图
CREATE VIEW treasury_account_analysis AS
SELECT
    account_no,
    account_name,
    COUNT(*) as tran_count,
    SUM(CASE WHEN dc_flag = 'C' THEN tran_amt ELSE 0 END) as total_income,
    SUM(CASE WHEN dc_flag = 'D' THEN tran_amt ELSE 0 END) as total_expense,
    AVG(tran_amt) as avg_amount,
    MAX(tran_amt) as max_amount,
    MIN(tran_amt) as min_amount
FROM treasury_transaction_detail
GROUP BY account_no, account_name;
```

### 缓存策略

- **Redis 缓存**: 热点账户余额数据
- **本地缓存**: 账户基础信息
- **分布式缓存**: 交易明细数据

### 监控告警

- **余额预警**: 账户余额低于阈值
- **大额交易预警**: 超过限额的交易
- **异常交易预警**: 异常时间或异常对手方
- **系统连接预警**: 司库系统连接异常

---

**文档版本**: v1.0
**更新日期**: 2025-06-23
**数据来源**: 中信银行天元司库 ERP 接口说明书 V7.7
