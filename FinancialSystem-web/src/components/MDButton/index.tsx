/**
=========================================================
* Material Dashboard 2 React - v1.0.0
=========================================================

* Product Page: https://www.creative-tim.com/product/soft-ui-dashboard-pro-react
* Copyright 2021 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/
import React, { forwardRef } from 'react';

// Custom styles for MDButton
import MDButtonRoot from 'components/MDButton/MDButtonRoot.js';

// Material Dashboard 2 React contexts
import { useMaterialUIController } from 'context';

// Import type
import { MDButtonProps } from 'types/components';

const MDButton = forwardRef<HTMLButtonElement, MDButtonProps>(
  (
    {
      color = 'white',
      variant = 'contained',
      size = 'medium',
      circular = false,
      iconOnly = false,
      children,
      ...rest
    },
    ref
  ) => {
    const [controller] = useMaterialUIController();
    const { darkMode } = controller;

    return (
      <MDButtonRoot
        {...rest}
        ref={ref}
        ownerState={{ color, variant, size, circular, iconOnly, darkMode }}
      >
        {children}
      </MDButtonRoot>
    );
  }
);

MDButton.displayName = 'MDButton';

export default MDButton;
