/**
=========================================================
* Material Dashboard 2 React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/
import React, { forwardRef } from 'react';

// Custom styles for MDBox
import MDBoxRoot from 'components/MDBox/MDBoxRoot';

// Import type from types/components.ts
import { MDBoxProps } from 'types/components';

const MDBox = forwardRef<HTMLDivElement, MDBoxProps>(
  (
    {
      variant = 'contained',
      bgColor = 'transparent',
      color = 'dark',
      opacity = 1,
      borderRadius = 'none',
      shadow = 'none',
      coloredShadow = 'none',
      ...rest
    },
    ref
  ) => (
    <MDBoxRoot
      ref={ref}
      ownerState={{ variant, bgColor, color, opacity, borderRadius, shadow, coloredShadow }}
      {...rest}
    />
  )
);

MDBox.displayName = 'MDBox';

export default MDBox;
