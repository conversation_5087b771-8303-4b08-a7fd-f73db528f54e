import PropTypes from 'prop-types';
import { Routes, Route, Navigate } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';

// Pages
import SignIn from '../layouts/authentication/sign-in';
import SignUp from '../layouts/authentication/sign-up';
import AssetManagement from '../layouts/assetmanagement';
import OverdueStatistics from '../layouts/debtmanagement/pages/OverdueStatistics';
import OverdueDebtAdd from '../layouts/debtmanagement/pages/OverdueDebtAdd';
import OverdueReductionUpdate from '../layouts/debtmanagement/pages/OverdueReductionUpdate';
import DebtSearch from '../layouts/debtmanagement/pages/DebtSearch';
import LitigationConversion from '../layouts/debtmanagement/pages/LitigationConversion';
import DebtList from '../layouts/debtmanagement/pages/DebtList';
import UserManagement from '../layouts/usermanagement/index.jsx';
import ManagementReport from '../layouts/managementreport';
import DataMonitor from '../layouts/datamonitor';
import DataExportCenter from '../layouts/dataexport/pages/DataExportCenter';
import DataPermissions from '../layouts/dataPermissions';
import OAWorkflowExtractor from '../layouts/oaworkflow';

/**
 * 统一的路由配置组件
 * 避免RTL和非RTL模式下路由配置不一致的问题
 */
const AppRoutes = ({ isAuthenticated, authLoading }) => {
  return (
    <Routes>
      {/* 公共路由 - 登录和注册不需要认证 */}
      <Route path="/authentication/sign-in" element={<SignIn />} />
      <Route path="/authentication/sign-up" element={<SignUp />} />

      {/* 根路径重定向 */}
      <Route
        path="/"
        element={
          authLoading ? (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
              }}
            >
              <div
                style={{
                  textAlign: 'center',
                  padding: '20px',
                  backgroundColor: 'white',
                  borderRadius: '8px',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                }}
              >
                <div style={{ fontSize: '18px', marginBottom: '10px' }}>🔄 系统初始化中...</div>
                <div style={{ fontSize: '14px', color: '#666' }}>正在验证用户身份，请稍候</div>
              </div>
            </div>
          ) : (
            <Navigate
              to={
                isAuthenticated ? '/debt-management/Overdue-statistics' : '/authentication/sign-in'
              }
              replace
            />
          )
        }
      />

      {/* 受保护路由 - 需要登录才能访问 */}
      <Route element={<ProtectedRoute />}>
        <Route path="/asset-management/*" element={<AssetManagement />} />

        {/* 债权管理子路由 */}
        <Route path="/debt-management" element={<DebtList />}>
          <Route index element={<Navigate to="Overdue-statistics" replace />} />
          <Route path="Overdue-statistics" element={<OverdueStatistics />} />
          <Route path="OverdueDebt-form" element={<OverdueDebtAdd />} />
          <Route path="OverdueDebt-reduction" element={<OverdueReductionUpdate />} />
        </Route>

        {/* 债权债务记录查询 - 独立路由 */}
        <Route path="/debt-management/debt-search" element={<DebtSearch />} />

        {/* 诉讼和非诉讼互转 - 独立路由 */}
        <Route path="/debt-management/litigation-conversion" element={<LitigationConversion />} />

        {/* 管理报表路由 */}
        <Route path="/management-report" element={<ManagementReport />} />

        {/* 数据导出中心路由 */}
        <Route path="/data-export/center" element={<DataExportCenter />} />

        {/* OA工作流提取路由 */}
        <Route path="/oa-workflow" element={<OAWorkflowExtractor />} />
      </Route>

      {/* 管理员路由 - 需要ROLE_ADMIN角色 */}
      <Route element={<ProtectedRoute adminOnly />}>
        <Route path="/user-management/*" element={<UserManagement />} />
        <Route path="/data-permissions" element={<DataPermissions />} />
        <Route path="/data-monitor/*" element={<DataMonitor />} />
      </Route>

      {/* 未匹配路由处理 */}
      <Route
        path="*"
        element={
          <Navigate
            to={isAuthenticated ? '/debt-management/Overdue-statistics' : '/authentication/sign-in'}
            replace
          />
        }
      />
    </Routes>
  );
};

AppRoutes.propTypes = {
  isAuthenticated: PropTypes.bool.isRequired,
  authLoading: PropTypes.bool.isRequired,
};

export default AppRoutes;
