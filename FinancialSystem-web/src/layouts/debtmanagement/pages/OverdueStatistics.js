import React, { useEffect, useState } from 'react';
import { Box, Grid, Typography, Button } from '@mui/material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import OverdueStatisticsFilter from '../components/OverdueStatisticsFilter';
import { fetchDebtStatistics, fetchDebtStatisticsDetail } from '../data/DebtStatisticsData';
import DebtStatisticsChart from '../components/DebtStatisticsChart';
import DownloadExcelButton from '../components/DownloadExcelButton';
import CustomPieChart from '../components/CustomPieChart';
import BarStatisticsChart from '../components/BarStatisticsChart';
import GenericDataTable from 'components/tables/GenericDataTable';

// 逾期债权看板页面
const OverdueStatistics = () => {
  // 定义看板中直接从后端获取的数据
  const [debtStatistics, setDebtStatistics] = useState({
    totalReductionAmount: 0.0,
    totalDebtBalance: 0.0,
    initialDebtBalance: 0.0,
    initialDebtReductionAmount: 0.0,
    initialDebtEndingBalance: 0.0,
    newDebtAmount: 0.0,
    newDebtReductionAmount: 0.0,
    newDebtBalance: 0.0,
    newDebtSummaryByCompany: [],
    existingDebtSummaryByCompany: [],
    monthNewReductionDebtByCompany: [],
    // monthNewDebtByCompany: [],
  });

  // 添加当前筛选条件状态
  const [currentFilters, setCurrentFilters] = useState({
    year: new Date().getFullYear().toString(),
    month: (new Date().getMonth() + 1).toString().padStart(2, '0') + '月',
    company: '全部',
  });

  // 直接从后端获取数据
  useEffect(() => {
    let isMounted = true;

    const fetchInitialData = async () => {
      try {
        // 获取当前日期作为默认值
        const currentDate = new Date();
        const defaultYear = currentDate.getFullYear().toString();
        const defaultMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0') + '月';
        const defaultCompany = '全部'; // 默认查询所有公司

        // 设置初始筛选条件
        setCurrentFilters({
          year: defaultYear,
          month: defaultMonth,
          company: defaultCompany,
        });

        const data = await fetchDebtStatistics(defaultYear, defaultMonth, defaultCompany);

        if (!isMounted) {
          return;
        }

        // 在更新前先检查数据是否已经变化，避免不必要的渲染
        setDebtStatistics(prevState => {
          if (
            prevState.totalReductionAmount === data.totalReductionAmount &&
            prevState.totalDebtBalance === data.totalDebtBalance &&
            prevState.initialDebtBalance === data.initialDebtBalance &&
            prevState.initialDebtReductionAmount === data.initialDebtReductionAmount &&
            prevState.initialDebtEndingBalance === data.initialDebtEndingBalance &&
            prevState.newDebtAmount === data.newDebtAmount &&
            prevState.newDebtReductionAmount === data.newDebtReductionAmount &&
            prevState.newDebtBalance === data.newDebtBalance
          ) {
            return prevState; // 数据没有变化，直接返回原状态
          }

          return {
            ...prevState,
            totalReductionAmount: data.totalReductionAmount || 0,
            totalDebtBalance: data.totalDebtBalance || 0,
            initialDebtBalance: data.initialDebtBalance || 0,
            initialDebtReductionAmount: data.initialDebtReductionAmount || 0,
            initialDebtEndingBalance: data.initialDebtEndingBalance || 0,
            newDebtAmount: data.newDebtAmount || 0,
            newDebtReductionAmount: data.newDebtReductionAmount || 0,
            newDebtBalance: data.newDebtBalance || 0,
            newDebtSummaryByCompany: data.newDebtSummaryByCompany || [],
            existingDebtSummaryByCompany: data.existingDebtSummaryByCompany || [],
            monthNewReductionDebtByCompany: data.monthNewReductionDebtByCompany || [],
          };
        });
      } catch (error) {
        if (isMounted) {
          console.error('Error fetching debt statistics:', error);
        }
      }
    };

    fetchInitialData();

    return () => {
      isMounted = false;
    };
  }, []); // 只依赖空数组，确保只在组件挂载时触发一次
  // 定义逾期债权明细表数据变量
  const [debtStatisticsDetail, setDebtStatisticsDetail] = useState({
    newDebtDetailList: [],
    reductionDebtDetailList: [],
  });
  // 从后端获取逾期债权明细表数据（useEffect 获取的数据只是初次加载时的债权明细数据）
  useEffect(() => {
    let isMounted = true;

    const fetchInitialDetailData = async () => {
      try {
        // 获取当前日期作为默认值
        const currentDate = new Date();
        const defaultYear = currentDate.getFullYear().toString();
        const defaultMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0') + '月';
        const defaultCompany = '全部'; // 默认查询所有公司

        const data = await fetchDebtStatisticsDetail(defaultYear, defaultMonth, defaultCompany);

        if (!isMounted) {
          return;
        }

        setDebtStatisticsDetail(prevState => ({
          ...prevState,
          newDebtDetailList: data.newDebtDetailList || [],
          reductionDebtDetailList: data.reductionDebtDetailList || [],
        }));
      } catch (error) {
        if (isMounted) {
          console.error('Error fetching debt statistics detail:', error);
        }
      }
    };

    fetchInitialDetailData();

    return () => {
      isMounted = false;
    };
  }, []);

  // 后续根据从前端的参数从后端获取数据
  const getDebtStatisticsData = async (year, month, company) => {
    try {
      // 更新当前筛选条件
      setCurrentFilters({
        year,
        month,
        company,
      });

      // 获取汇总统计数据
      const statistics = await fetchDebtStatistics(year, month, company);
      setDebtStatistics(statistics);

      // 获取详细统计数据
      const statisticsDetail = await fetchDebtStatisticsDetail(year, month, company);
      setDebtStatisticsDetail(statisticsDetail);
    } catch (error) {
      console.error('Error fetching debt statistics:', error);
    }
  };
  // 圆饼图数据
  const chartData1 = [
    { name: '存量债权', value: debtStatistics.initialDebtBalance, color: '#2E86C1' },
    { name: '新增债权', value: debtStatistics.newDebtAmount, color: '#27AE60' },
  ];
  const chartData2 = [
    { name: '存量债权', value: debtStatistics.totalReductionAmount, color: '#2E86C1' },
    { name: '新增债权', value: debtStatistics.newDebtReductionAmount, color: '#27AE60' },
  ];
  const chartData3 = [
    { name: '存量债权', value: debtStatistics.initialDebtEndingBalance, color: '#2E86C1' },
    { name: '新增债权', value: debtStatistics.newDebtBalance, color: '#27AE60' },
  ];
  const summaryText = '年初逾期债权目标位58,442万元';

  // 直接在各个GenericDataTable中定义列，避免未使用的变量

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Box
        sx={{
          maxWidth: '1600px', // 设置最大宽度
          margin: '0 auto', // 居中显示
          padding: '24px', // 统一内边距
          width: '100%', // 占满可用宽度
        }}
      >
        {/*一、 添加年份、月份和管理公司选择框 */}
        <Box sx={{ width: '100%' }}>
          <OverdueStatisticsFilter onSearch={getDebtStatisticsData} />
        </Box>

        <Box sx={{ marginBottom: '20px' }} />

        {/*二、标题*/}
        <Typography
          variant="h4"
          gutterBottom
          align="center"
          sx={{
            fontWeight: 'bold',
            fontSize: '24px',
            color: 'black',
            marginBottom: '30px',
          }}
        >
          逾期债权看板
        </Typography>

        {/*三、汇总逾期债权数据*/}
        <Grid
          container
          spacing={3}
          sx={{
            width: '100%',
            margin: '0',
            '& > .MuiGrid-item': {
              paddingTop: '20px',
              paddingBottom: '20px',
              position: 'relative',
            },
          }}
        >
          {/* 每个饼图容器 */}
          <Grid
            item
            xs={12}
            md={4}
            sx={{
              border: '1px solid #d3d3d3',
              backgroundColor: '#FFFFFF',
              borderRadius: '8px',
              height: '400px',
              padding: '15px',
              boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.05)',
            }}
          >
            <CustomPieChart title="逾期债权目标" data={chartData1} summaryText={summaryText} />
            <Button
              sx={{
                position: 'absolute',
                bottom: '30px',
                right: '30px',
                fontSize: '12px',
                padding: '4px 10px',
                backgroundColor: '#e2e2e2',
                color: '#333',
                '&:hover': {
                  backgroundColor: '#d0d0d0',
                },
                zIndex: 10,
              }}
            >
              更多信息
            </Button>
          </Grid>

          <Grid
            item
            xs={12}
            md={4}
            sx={{
              border: '1px solid #d3d3d3',
              backgroundColor: '#FFFFFF',
              borderRadius: '8px',
              height: '400px',
              padding: '15px',
              boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.05)',
            }}
          >
            <CustomPieChart title="处置金额" data={chartData2} />
            <Button
              sx={{
                position: 'absolute',
                bottom: '30px',
                right: '30px',
                fontSize: '12px',
                padding: '4px 10px',
                backgroundColor: '#e2e2e2',
                color: '#333',
                '&:hover': {
                  backgroundColor: '#d0d0d0',
                },
                zIndex: 10,
              }}
            >
              更多信息
            </Button>
          </Grid>

          <Grid
            item
            xs={12}
            md={4}
            sx={{
              border: '1px solid #d3d3d3',
              backgroundColor: '#FFFFFF',
              borderRadius: '8px',
              height: '400px',
              padding: '15px',
              boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.05)',
            }}
          >
            <CustomPieChart title="逾期债权余额" data={chartData3} />
            <Button
              sx={{
                position: 'absolute',
                bottom: '30px',
                right: '30px',
                fontSize: '12px',
                padding: '4px 10px',
                backgroundColor: '#e2e2e2',
                color: '#333',
                '&:hover': {
                  backgroundColor: '#d0d0d0',
                },
                zIndex: 10,
              }}
            >
              更多信息
            </Button>
          </Grid>
        </Grid>

        <Box sx={{ marginBottom: '20px' }} />

        {/*四、管理公司月度新增处置情况表*/}
        {debtStatistics.monthNewReductionDebtByCompany &&
          debtStatistics.monthNewReductionDebtByCompany.length > 0 && (
          <Grid
            container
            sx={{
              width: '100%',
              margin: '20px 0',
              border: '1px solid #d3d3d3',
              backgroundColor: '#FFFFFF',
              borderRadius: '12px',
              padding: '20px',
              boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                width: '100%',
                overflowY: 'hidden',
                overflowX: 'auto',
                '&::-webkit-scrollbar': {
                  height: '8px',
                  width: '0px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: '#f1f1f1',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#888',
                  borderRadius: '4px',
                  '&:hover': {
                    backgroundColor: '#555',
                  },
                },
              }}
            >
              <Box
                sx={{
                  minWidth: '800px',
                  width: '100%',
                }}
              >
                <div
                  style={{
                    width: '100%',
                    height: '400px',
                    backgroundColor: '#FFFFFF',
                    borderRadius: '12px',
                    padding: '16px',
                  }}
                >
                  <BarStatisticsChart
                    data={debtStatistics.monthNewReductionDebtByCompany}
                    chartTitle="管理公司月度新增处置情况表"
                    chartDebtLabel="newAmount"
                  />
                </div>
              </Box>
            </Box>
          </Grid>
        )}

        {/*五、逾期债权按管理公司汇总*/}
        {((debtStatistics.existingDebtSummaryByCompany &&
          debtStatistics.existingDebtSummaryByCompany.length > 0) ||
          (debtStatistics.newDebtSummaryByCompany &&
            debtStatistics.newDebtSummaryByCompany.length > 0)) && (
          <Grid
            item
            xs={12}
            md={6}
            sx={{
              border: '1px solid #d3d3d3',
              backgroundColor: '#FFFFFF',
              borderRadius: '12px',
              padding: '20px',
              boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
              margin: '20px 0',
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                width: '100%',
                overflowY: 'hidden',
                overflowX: 'auto',
                '&::-webkit-scrollbar': {
                  height: '8px',
                  width: '0px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: '#f1f1f1',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: '#888',
                  borderRadius: '4px',
                  '&:hover': {
                    backgroundColor: '#555',
                  },
                },
              }}
            >
              <Box
                sx={{
                  minWidth: '800px',
                  width: '100%',
                }}
              >
                {debtStatistics.existingDebtSummaryByCompany &&
                  debtStatistics.existingDebtSummaryByCompany.length > 0 && (
                  <div
                    style={{
                      width: '100%',
                      height: '400px',
                      backgroundColor: '#FFFFFF',
                      borderRadius: '12px',
                      padding: '16px',
                      marginBottom: '20px',
                    }}
                  >
                    <DebtStatisticsChart
                      data={debtStatistics.existingDebtSummaryByCompany}
                      chartTitle="管理公司新增逾期债权统计表"
                      chartDebtLabel="新增债权"
                    />
                  </div>
                )}

                {debtStatistics.newDebtSummaryByCompany &&
                  debtStatistics.newDebtSummaryByCompany.length > 0 && (
                  <div
                    style={{
                      width: '100%',
                      height: '400px',
                      backgroundColor: '#FFFFFF',
                      borderRadius: '12px',
                      padding: '16px',
                    }}
                  >
                    <DebtStatisticsChart
                      data={debtStatistics.newDebtSummaryByCompany}
                      chartTitle="管理公司存量逾期债权统计表"
                      chartDebtLabel="存量债权"
                    />
                  </div>
                )}
              </Box>
            </Box>
          </Grid>
        )}

        {/*新增债权明细表*/}
        {debtStatisticsDetail.newDebtDetailList &&
          debtStatisticsDetail.newDebtDetailList.length > 0 && (
          <Grid
            item
            xs={12}
            md={6}
            style={{
              border: '1px solid #d3d3d3',
              backgroundColor: '#FFFFFF',
              borderRadius: '12px',
              padding: '20px',
              boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
              width: '100%',
            }}
          >
            <div>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={10}>
                  <Typography variant="h5" align="left" sx={{ fontWeight: 'bold' }}>
                      新增债权明细表
                  </Typography>
                </Grid>
                <Grid item xs={2} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <DownloadExcelButton
                    type="NewDebtDetails"
                    year={currentFilters.year}
                    month={currentFilters.month}
                    company={currentFilters.company}
                  />
                </Grid>
              </Grid>
              <div style={{ marginTop: '10px' }}>
                <GenericDataTable
                  columns={[
                    { field: '管理公司', headerName: '管理公司', width: '8%' },
                    { field: '债权人', headerName: '债权人', width: '25%' },
                    { field: '债务人', headerName: '债务人', width: '25%' },
                    { field: '新增金额', headerName: '新增金额', width: '8%', type: 'number' },
                    { field: '处置金额', headerName: '处置金额', width: '8%', type: 'number' },
                    { field: '债权余额', headerName: '债权余额', width: '8%', type: 'number' },
                  ]}
                  data={debtStatisticsDetail.newDebtDetailList}
                  pageSize={8}
                />
              </div>
            </div>
          </Grid>
        )}

        {/*处置债权明细表*/}
        {debtStatisticsDetail.reductionDebtDetailList &&
          debtStatisticsDetail.reductionDebtDetailList.length > 0 && (
          <Grid
            item
            xs={12}
            md={6}
            style={{
              border: '1px solid #d3d3d3',
              backgroundColor: '#FFFFFF',
              borderRadius: '12px',
              padding: '20px',
              boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
              width: '100%',
              marginTop: '20px',
            }}
          >
            <div>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={10}>
                  <Typography
                    variant="h5"
                    align="left"
                    sx={{ fontWeight: 'bold', fontSize: '18px', marginBottom: '10px' }}
                  >
                      处置债权明细表
                  </Typography>
                </Grid>
                <Grid item xs={2} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <DownloadExcelButton
                    type="ReductionDebtDetails"
                    year={currentFilters.year}
                    month={currentFilters.month}
                    company={currentFilters.company}
                  />
                </Grid>
              </Grid>
              <div style={{ marginTop: '10px' }}>
                <GenericDataTable
                  columns={[
                    { field: '管理公司', headerName: '管理公司', width: '8%' },
                    { field: '债权人', headerName: '债权人', width: '25%' },
                    { field: '债务人', headerName: '债务人', width: '25%' },
                    { field: '期间', headerName: '期间', width: '10%' },
                    { field: '是否涉诉', headerName: '是否涉诉', width: '8%' },
                    {
                      field: '累计处置金额',
                      headerName: '累计处置金额',
                      width: '10%',
                      type: 'number',
                    },
                  ]}
                  data={debtStatisticsDetail.reductionDebtDetailList}
                  pageSize={8}
                />
              </div>
            </div>
          </Grid>
        )}
      </Box>
    </DashboardLayout>
  );
};

export default OverdueStatistics;
