import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Box,
  Typography,
} from '@mui/material';
import { ASSET_TYPES, ASSET_TYPE_DESCRIPTIONS } from '../services/assetService';

const AssetForm = ({ open, onClose, onSubmit, initialData = null, loading = false }) => {
  const [formData, setFormData] = useState({
    assetName: '',
    propertyOwnerId: 1,
    managementCompany: '深圳万润科技股份有限公司',
    propertyCertificateNo: '',
    hasPropertyCertificate: true,
    acquisitionDate: '',
    purchaseContractNo: '',
    purchasePrice: '',
    location: '',
    totalArea: '',
    propertyYears: '',
    usedYears: '',
    remainingYears: '',
    assetType: 'PROPERTY',
    currentSelfUseArea: '',
    currentRentalArea: '',
    currentIdleArea: '',
    ...initialData,
  });

  const [errors, setErrors] = useState({});

  const handleChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null,
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.assetName.trim()) {
      newErrors.assetName = '资产名称不能为空';
    }
    
    if (!formData.totalArea || parseFloat(formData.totalArea) <= 0) {
      newErrors.totalArea = '总面积必须大于0';
    }
    
    if (!formData.managementCompany.trim()) {
      newErrors.managementCompany = '管理公司不能为空';
    }
    
    if (formData.purchasePrice && parseFloat(formData.purchasePrice) < 0) {
      newErrors.purchasePrice = '购买价格不能为负数';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      const submitData = {
        ...formData,
        purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : null,
        totalArea: parseFloat(formData.totalArea),
        propertyYears: formData.propertyYears ? parseInt(formData.propertyYears) : null,
        usedYears: formData.usedYears ? parseInt(formData.usedYears) : null,
        remainingYears: formData.remainingYears ? parseInt(formData.remainingYears) : null,
        currentSelfUseArea: formData.currentSelfUseArea ? parseFloat(formData.currentSelfUseArea) : null,
        currentRentalArea: formData.currentRentalArea ? parseFloat(formData.currentRentalArea) : null,
        currentIdleArea: formData.currentIdleArea ? parseFloat(formData.currentIdleArea) : null,
      };
      onSubmit(submitData);
    }
  };

  const handleClose = () => {
    setFormData({
      assetName: '',
      propertyOwnerId: 1,
      managementCompany: '深圳万润科技股份有限公司',
      propertyCertificateNo: '',
      hasPropertyCertificate: true,
      acquisitionDate: '',
      purchaseContractNo: '',
      purchasePrice: '',
      location: '',
      totalArea: '',
      propertyYears: '',
      usedYears: '',
      remainingYears: '',
      assetType: 'PROPERTY',
      currentSelfUseArea: '',
      currentRentalArea: '',
      currentIdleArea: '',
    });
    setErrors({});
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {initialData ? '编辑资产' : '新增资产'}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="资产名称"
                value={formData.assetName}
                onChange={handleChange('assetName')}
                error={!!errors.assetName}
                helperText={errors.assetName}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>资产类型</InputLabel>
                <Select
                  value={formData.assetType}
                  onChange={handleChange('assetType')}
                  label="资产类型"
                >
                  {Object.entries(ASSET_TYPES).map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {ASSET_TYPE_DESCRIPTIONS[value]}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="位置"
                value={formData.location}
                onChange={handleChange('location')}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="总面积(平方米)"
                type="number"
                value={formData.totalArea}
                onChange={handleChange('totalArea')}
                error={!!errors.totalArea}
                helperText={errors.totalArea}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="购买价格(元)"
                type="number"
                value={formData.purchasePrice}
                onChange={handleChange('purchasePrice')}
                error={!!errors.purchasePrice}
                helperText={errors.purchasePrice}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="获取时间"
                type="date"
                value={formData.acquisitionDate}
                onChange={handleChange('acquisitionDate')}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="购买合同编号"
                value={formData.purchaseContractNo}
                onChange={handleChange('purchaseContractNo')}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="权属证号"
                value={formData.propertyCertificateNo}
                onChange={handleChange('propertyCertificateNo')}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.hasPropertyCertificate}
                    onChange={handleChange('hasPropertyCertificate')}
                  />
                }
                label="是否有产权证"
              />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                面积分配
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="自用面积(平方米)"
                type="number"
                value={formData.currentSelfUseArea}
                onChange={handleChange('currentSelfUseArea')}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="出租面积(平方米)"
                type="number"
                value={formData.currentRentalArea}
                onChange={handleChange('currentRentalArea')}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="闲置面积(平方米)"
                type="number"
                value={formData.currentIdleArea}
                onChange={handleChange('currentIdleArea')}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          取消
        </Button>
        <Button onClick={handleSubmit} variant="contained" disabled={loading}>
          {loading ? '保存中...' : '保存'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AssetForm;
