import React from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
} from '@mui/material';

// eslint-disable-next-line react/prop-types
const AssetDetails = ({ selectedTab, handleTabChange, properties, lands }) => {
  const renderTable = data => (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>资产名称</TableCell>
            <TableCell>类型</TableCell>
            <TableCell align="right">总面积(㎡)</TableCell>
            <TableCell align="right">购买价格(万元)</TableCell>
            <TableCell align="right">自用面积(㎡)</TableCell>
            <TableCell align="right">租赁面积(㎡)</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((item, index) => (
            <TableRow key={index}>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.type}</TableCell>
              <TableCell align="right">{item.area.toLocaleString()}</TableCell>
              <TableCell align="right">{(item.purchasePrice / 10000).toLocaleString()}</TableCell>
              <TableCell align="right">{item.selfUseArea.toLocaleString()}</TableCell>
              <TableCell align="right">{item.rentalArea.toLocaleString()}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={selectedTab} onChange={handleTabChange}>
          <Tab label="房产信息" />
          <Tab label="土地信息" />
        </Tabs>
      </Box>

      {selectedTab === 0 && renderTable(properties)}
      {selectedTab === 1 && renderTable(lands)}
    </>
  );
};

export default AssetDetails;
