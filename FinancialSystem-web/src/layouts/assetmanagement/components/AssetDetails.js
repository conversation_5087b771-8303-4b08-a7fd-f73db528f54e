import React, { useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
  Button,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import AssetForm from './AssetForm';
import NotificationSnackbar from './NotificationSnackbar';
import ConfirmDialog from './ConfirmDialog';

// eslint-disable-next-line react/prop-types
const AssetDetails = ({ selectedTab, handleTabChange, properties, lands, loading, apiConnected, onAssetCreate, onAssetUpdate, onAssetDelete }) => {
  const [formOpen, setFormOpen] = useState(false);
  const [editingAsset, setEditingAsset] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  // 通知状态
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    onConfirm: null,
    loading: false
  });

  const handleAddAsset = () => {
    setEditingAsset(null);
    setFormOpen(true);
  };

  const handleEditAsset = (asset) => {
    setEditingAsset(asset);
    setFormOpen(true);
  };

  const handleDeleteAsset = (asset) => {
    setConfirmDialog({
      open: true,
      title: '删除资产',
      message: `确定要删除资产"${asset.name}"吗？此操作不可撤销。`,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));
        try {
          await onAssetDelete(asset.id);
          setNotification({
            open: true,
            message: `资产"${asset.name}"删除成功`,
            severity: 'success',
          });
          setConfirmDialog({ open: false, title: '', message: '', onConfirm: null, loading: false });
        } catch (error) {
          setNotification({
            open: true,
            message: '删除失败：' + error.message,
            severity: 'error',
          });
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false,
    });
  };

  const handleFormSubmit = async (formData) => {
    setFormLoading(true);
    try {
      if (editingAsset) {
        await onAssetUpdate(editingAsset.id, formData);
        setNotification({
          open: true,
          message: '资产更新成功',
          severity: 'success',
        });
      } else {
        await onAssetCreate(formData);
        setNotification({
          open: true,
          message: '资产创建成功',
          severity: 'success',
        });
      }
      setFormOpen(false);
      setEditingAsset(null);
    } catch (error) {
      setNotification({
        open: true,
        message: '保存失败：' + error.message,
        severity: 'error',
      });
    } finally {
      setFormLoading(false);
    }
  };

  const handleFormClose = () => {
    setFormOpen(false);
    setEditingAsset(null);
  };

  const handleNotificationClose = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  const handleConfirmDialogClose = () => {
    setConfirmDialog({ open: false, title: '', message: '', onConfirm: null, loading: false });
  };
  const renderTable = data => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>正在加载数据...</Typography>
        </Box>
      );
    }

    if (!data || data.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography color="text.secondary">
            暂无{selectedTab === 0 ? '房产' : '土地'}数据
          </Typography>
        </Box>
      );
    }

    return (
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>资产名称</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>位置</TableCell>
              <TableCell align="right">总面积(㎡)</TableCell>
              <TableCell align="right">购买价格(万元)</TableCell>
              <TableCell align="right">自用面积(㎡)</TableCell>
              <TableCell align="right">租赁面积(㎡)</TableCell>
              <TableCell>产权证</TableCell>
              {apiConnected && <TableCell>数据来源</TableCell>}
              <TableCell align="center">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((item, index) => (
              <TableRow key={item.id || index}>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.type}</TableCell>
                <TableCell>{item.location || '-'}</TableCell>
                <TableCell align="right">{item.area.toLocaleString()}</TableCell>
                <TableCell align="right">{(item.purchasePrice / 10000).toLocaleString()}</TableCell>
                <TableCell align="right">{item.selfUseArea.toLocaleString()}</TableCell>
                <TableCell align="right">{item.rentalArea.toLocaleString()}</TableCell>
                <TableCell>
                  {item.hasPropertyCertificate ? (
                    <Typography variant="caption" color="success.main">
                      有证 ({item.propertyCertificateNo || '未填写'})
                    </Typography>
                  ) : (
                    <Typography variant="caption" color="error.main">
                      无证
                    </Typography>
                  )}
                </TableCell>
                {apiConnected && (
                  <TableCell>
                    <Typography variant="caption" color="success.main">
                      API数据
                    </Typography>
                  </TableCell>
                )}
                <TableCell align="center">
                  <Tooltip title="编辑">
                    <IconButton
                      size="small"
                      onClick={() => handleEditAsset(item)}
                      disabled={!apiConnected}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="删除">
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteAsset(item)}
                      disabled={!apiConnected}
                      color="error"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', flexGrow: 1 }}>
          <Tabs value={selectedTab} onChange={handleTabChange}>
            <Tab label="房产信息" />
            <Tab label="土地信息" />
          </Tabs>
        </Box>
        {apiConnected && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddAsset}
            sx={{ ml: 2 }}
          >
            新增{selectedTab === 0 ? '房产' : '土地'}
          </Button>
        )}
      </Box>

      {selectedTab === 0 && renderTable(properties)}
      {selectedTab === 1 && renderTable(lands)}

      <AssetForm
        open={formOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        initialData={editingAsset}
        loading={formLoading}
      />

      <NotificationSnackbar
        open={notification.open}
        message={notification.message}
        severity={notification.severity}
        onClose={handleNotificationClose}
      />

      <ConfirmDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onClose={handleConfirmDialogClose}
        onConfirm={confirmDialog.onConfirm}
        loading={confirmDialog.loading}
        severity="error"
      />
    </>
  );
};

export default AssetDetails;
