import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Box, Typography, Paper } from '@mui/material';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const AssetActivationBarChart = ({ data, title = "各公司资产盘活情况" }) => {
  // 如果没有数据，显示空状态
  if (!data || data.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Typography color="text.secondary">
          暂无数据
        </Typography>
      </Paper>
    );
  }

  const chartData = {
    labels: data.map(item => item.companyName || '未知公司'),
    datasets: [
      {
        label: '总面积(㎡)',
        data: data.map(item => item.totalArea || 0),
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
        yAxisID: 'y',
      },
      {
        label: '已利用面积(㎡)',
        data: data.map(item => (item.selfUseArea || 0) + (item.rentalArea || 0)),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
        yAxisID: 'y',
      },
      {
        label: '盘活率(%)',
        data: data.map(item => {
          const total = item.totalArea || 0;
          const used = (item.selfUseArea || 0) + (item.rentalArea || 0);
          return total > 0 ? ((used / total) * 100).toFixed(1) : 0;
        }),
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
        type: 'line',
        yAxisID: 'y1',
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            if (label.includes('率')) {
              return `${label}: ${value}%`;
            } else {
              return `${label}: ${value.toLocaleString()}㎡`;
            }
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: '公司',
        },
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: '面积(㎡)',
        },
        beginAtZero: true,
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: '盘活率(%)',
        },
        beginAtZero: true,
        max: 100,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom align="center">
        {title}
      </Typography>
      <Box sx={{ height: 400, position: 'relative' }}>
        <Bar data={chartData} options={options} />
      </Box>
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
        <Box>
          <Typography variant="body2" color="text.secondary">
            公司数量
          </Typography>
          <Typography variant="h6">
            {data.length}
          </Typography>
        </Box>
        <Box>
          <Typography variant="body2" color="text.secondary">
            平均盘活率
          </Typography>
          <Typography variant="h6" color="primary">
            {(() => {
              const totalActivationRate = data.reduce((sum, item) => {
                const total = item.totalArea || 0;
                const used = (item.selfUseArea || 0) + (item.rentalArea || 0);
                return sum + (total > 0 ? (used / total) * 100 : 0);
              }, 0);
              return data.length > 0 ? (totalActivationRate / data.length).toFixed(1) : 0;
            })()}%
          </Typography>
        </Box>
        <Box>
          <Typography variant="body2" color="text.secondary">
            总资产面积
          </Typography>
          <Typography variant="h6">
            {data.reduce((sum, item) => sum + (item.totalArea || 0), 0).toLocaleString()}㎡
          </Typography>
        </Box>
      </Box>
    </Paper>
  );
};

export default AssetActivationBarChart;
