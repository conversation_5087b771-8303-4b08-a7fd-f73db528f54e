/**
=========================================================
* Material Dashboard 2  React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

import React, { useMemo } from 'react';

// react-chartjs-2 components
import { Bar } from 'react-chartjs-2';

// Chart.js 统一配置
import '../../../utils/chartConfig';

// @mui material components
import Card from '@mui/material/Card';
import Divider from '@mui/material/Divider';
import Icon from '@mui/material/Icon';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

// ReportsBarChart configurations
import configs from 'examples/Charts/BarCharts/ReportsBarChart/configs';

interface ReportsBarChartProps {
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'dark';
  title: string;
  description?: string | React.ReactNode;
  date: string;
  chart: {
    labels?: string[];
    datasets?: {
      label: string;
      data: number[];
    };
  };
}

function ReportsBarChart({
  color = 'info',
  title,
  description = '',
  date,
  chart
}: ReportsBarChartProps) {
  const { data, options } = configs(chart.labels || [], chart.datasets || { label: '', data: [] });

  return (
    <Card sx={{ height: '100%' }}>
      <MDBox sx={{ p: 2 }}>
        {useMemo(
          () => (
            <MDBox
              variant="gradient"
              bgColor={color}
              borderRadius="lg"
              coloredShadow={color}
              sx={{ py: 2, pr: 0.5, mt: -5, height: '12.5rem' }}
            >
              <Bar data={data} options={options} redraw />
            </MDBox>
          ),
          [color, chart]
        )}
        <MDBox sx={{ pt: 3, pb: 1, px: 1 }}>
          <MDTypography variant="h6" textTransform="capitalize">
            {title}
          </MDTypography>
          <MDTypography component="div" variant="button" color="text" fontWeight="light">
            {description}
          </MDTypography>
          <Divider />
          <MDBox sx={{ display: 'flex', alignItems: 'center' }}>
            <MDTypography variant="button" color="text" lineHeight={1} sx={{ mt: 0.15, mr: 0.5 }}>
              <Icon>schedule</Icon>
            </MDTypography>
            <MDTypography variant="button" color="text" fontWeight="light">
              {date}
            </MDTypography>
          </MDBox>
        </MDBox>
      </MDBox>
    </Card>
  );
}

export default ReportsBarChart;
