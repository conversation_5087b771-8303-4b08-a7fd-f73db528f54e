/**
=========================================================
* Material Dashboard 2  React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

import { ChartOptions, ChartData, InteractionMode } from 'chart.js';

interface DatasetConfig {
  label: string;
  data: number[];
}

interface ConfigsReturn {
  data: ChartData<'bar'>;
  options: ChartOptions<'bar'>;
}

function configs(labels: string[], datasets: DatasetConfig): ConfigsReturn {
  return {
    data: {
      labels,
      datasets: [
        {
          label: datasets.label,
          borderWidth: 0,
          borderRadius: 4,
          borderSkipped: false,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          data: datasets.data,
          maxBarThickness: 6
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      interaction: {
        intersect: false,
        mode: 'index' as InteractionMode
      },
      scales: {
        y: {
          beginAtZero: true,
          border: {
            display: false
          },
          grid: {
            display: true,
            drawOnChartArea: true,
            drawTicks: false,
            color: 'rgba(255, 255, 255, .2)'
          },
          ticks: {
            padding: 10,
            font: {
              size: 14,
              weight: '300' as const,
              family: 'Roboto',
              style: 'normal' as const,
              lineHeight: 2
            },
            color: '#fff'
          }
        },
        x: {
          border: {
            display: false
          },
          grid: {
            display: true,
            drawOnChartArea: true,
            drawTicks: false,
            color: 'rgba(255, 255, 255, .2)'
          },
          ticks: {
            display: true,
            color: '#f8f9fa',
            padding: 10,
            font: {
              size: 14,
              weight: '300' as const,
              family: 'Roboto',
              style: 'normal' as const,
              lineHeight: 2
            }
          }
        }
      }
    }
  };
}

export default configs;
