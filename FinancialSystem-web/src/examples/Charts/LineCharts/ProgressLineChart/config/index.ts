/**
=========================================================
* Material Dashboard 2 React - v2.1.0
=========================================================

* Product Page: https://www.creative-tim.com/product/nextjs-material-dashboard-pro
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// Material Dashboard 2 React base styles
import colors from 'assets/theme/base/colors';
import typography from 'assets/theme/base/typography';
import { ChartOptions, ChartData, InteractionMode } from 'chart.js';

const { gradients } = colors;

// Helper function to get gradient color
const getGradientColor = (colorKey: string): { main: string } => {
  const gradientMap = gradients as Record<string, { main: string; state: string }>;
  if (colorKey in gradientMap) {
    return gradientMap[colorKey];
  }
  return gradients.dark;
};

interface ConfigsReturn {
  data: ChartData<'line'>;
  options: ChartOptions<'line'>;
}

function configs(color: string, labels: string[], label: string, data: number[]): ConfigsReturn {
  return {
    data: {
      labels,
      datasets: [
        {
          label,
          tension: 0,
          pointRadius: 3,
          pointBackgroundColor: getGradientColor(color).main,
          borderColor: getGradientColor(color).main,
          borderWidth: 4,
          backgroundColor: 'transparent',
          fill: true,
          data
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      interaction: {
        intersect: false,
        mode: 'index' as InteractionMode
      },
      scales: {
        y: {
          grid: {
            // drawBorder is not a valid option in Chart.js v3+
            display: false,
            drawOnChartArea: true,
            drawTicks: false
            // borderDash is not a valid option in Chart.js v3+
          },
          ticks: {
            display: true,
            padding: 10,
            color: '#b2b9bf',
            font: {
              size: 11,
              family: typography.fontFamily,
              style: 'normal' as const,
              lineHeight: 2
            }
          }
        },
        x: {
          grid: {
            // drawBorder is not a valid option in Chart.js v3+
            display: true,
            drawOnChartArea: true,
            drawTicks: false,
            // borderDash is not a valid option in Chart.js v3+
            color: '#c1c4ce5c'
          },
          ticks: {
            display: true,
            color: '#b2b9bf',
            padding: 20,
            font: {
              size: 11,
              family: typography.fontFamily,
              style: 'normal' as const,
              lineHeight: 2
            }
          }
        }
      }
    }
  };
}

export default configs;
