import React, { useMemo } from 'react';

// react-chartjs-2 components
import { Line } from 'react-chartjs-2';

// Chart.js 统一配置
import '../../../../utils/chartConfig';

// @mui material components
import Card from '@mui/material/Card';
import Icon from '@mui/material/Icon';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDProgress from 'components/MDProgress';

// ProgressLineChart configurations
import configs from 'examples/Charts/LineCharts/ProgressLineChart/config';

interface ProgressLineChartProps {
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'dark';
  icon: React.ReactNode;
  title: string;
  count?: string | number;
  progress: number;
  height?: string | number;
  chart: {
    labels: string[];
    data: number[];
  };
}

function ProgressLineChart({
  color,
  icon,
  title,
  count,
  progress,
  height,
  chart
}: ProgressLineChartProps) {
  const { data, options } = configs(color || 'info', chart.labels || [], title, chart.data || []);

  return (
    <Card>
      <MDBox sx={{ display: 'flex', alignItems: 'center', pt: 2, px: 2 }}>
        <MDBox
          borderRadius="md"
          shadow="md"
          color="white"
          bgColor={color}
          variant="gradient"
          sx={{
            width: '3rem',
            height: '3rem',
            display: 'grid',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <Icon fontSize="medium">{icon}</Icon>
        </MDBox>
        <MDBox sx={{ ml: 2, lineHeight: 1 }}>
          <MDTypography
            variant="button"
            fontWeight="regular"
            textTransform="capitalize"
            color="text"
          >
            {title}
          </MDTypography>
          {count ? (
            <MDTypography variant="h5" fontWeight="bold">
              {count}
            </MDTypography>
          ) : null}
        </MDBox>
        <MDBox sx={{ width: '25%', ml: 'auto' }}>
          <MDTypography display="block" variant="caption" fontWeight="medium" color="text">
            {progress}%
          </MDTypography>
          <MDBox sx={{ mt: 0.25 }}>
            <MDProgress variant="gradient" color={color} value={progress} />
          </MDBox>
        </MDBox>
      </MDBox>
      {useMemo(
        () => (
          <MDBox sx={{ mt: 2 }}>
            <Line data={data} options={options} style={{ height }} redraw />
          </MDBox>
        ),
        [chart, height, color]
      )}
    </Card>
  );
}

// Setting default values for the props of ProgressLineChart
ProgressLineChart.defaultProps = {
  color: 'info',
  count: 0,
  height: '6.25rem'
};

export default ProgressLineChart;
