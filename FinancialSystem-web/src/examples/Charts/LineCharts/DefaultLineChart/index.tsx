import React, { useMemo } from 'react';

// react-chartjs-2 components
import { Line } from 'react-chartjs-2';

// Chart.js 统一配置
import '../../../../utils/chartConfig';

// @mui material components
import Card from '@mui/material/Card';
import Icon from '@mui/material/Icon';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

// DefaultLineChart configurations
import configs from 'examples/Charts/LineCharts/DefaultLineChart/configs';

// Material Dashboard 2 React base styles
import colors from 'assets/theme/base/colors';

interface DefaultLineChartProps {
  icon?: {
    color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark';
    component?: React.ReactNode;
  };
  title?: string;
  description?: string | React.ReactNode;
  height?: string | number;
  chart: {
    labels: string[];
    datasets: any;
  };
}

function DefaultLineChart({ icon, title, description, height, chart }: DefaultLineChartProps) {
  const chartDatasets = chart.datasets
    ? chart.datasets.map((dataset: any) => ({
        ...dataset,
        tension: 0,
        pointRadius: 3,
        borderWidth: 4,
        backgroundColor: 'transparent',
        fill: true,
        pointBackgroundColor: (colors as any)[dataset.color]
          ? (colors as any)[dataset.color || 'dark'].main
          : colors.dark.main,
        borderColor: (colors as any)[dataset.color]
          ? (colors as any)[dataset.color || 'dark'].main
          : colors.dark.main,
        maxBarThickness: 6
      }))
    : [];

  const { data, options } = configs(chart.labels || [], chartDatasets);

  const renderChart = (
    <MDBox sx={{ py: 2, pr: 2, pl: icon?.component ? 1 : 2 }}>
      {title || description ? (
        <MDBox sx={{ display: 'flex', px: description ? 1 : 0, pt: description ? 1 : 0 }}>
          {icon?.component && (
            <MDBox
              bgColor={icon.color || 'dark'}
              variant="gradient"
              coloredShadow={icon.color || 'dark'}
              borderRadius="xl"
              color="white"
              sx={{
                width: '4rem',
                height: '4rem',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                mt: -5,
                mr: 2
              }}
            >
              <Icon fontSize="medium">{icon.component}</Icon>
            </MDBox>
          )}
          <MDBox sx={{ mt: icon?.component ? -2 : 0 }}>
            {title && <MDTypography variant="h6">{title}</MDTypography>}
            <MDBox sx={{ mb: 2 }}>
              <MDTypography component="div" variant="button" color="text">
                {description}
              </MDTypography>
            </MDBox>
          </MDBox>
        </MDBox>
      ) : null}
      {useMemo(
        () => (
          <MDBox sx={{ height }}>
            <Line data={data} options={options} redraw />
          </MDBox>
        ),
        [chart, height]
      )}
    </MDBox>
  );

  return title || description ? <Card>{renderChart}</Card> : renderChart;
}

// Setting default values for the props of DefaultLineChart
DefaultLineChart.defaultProps = {
  icon: { color: 'info', component: '' },
  title: '',
  description: '',
  height: '19.125rem'
};

export default DefaultLineChart;
