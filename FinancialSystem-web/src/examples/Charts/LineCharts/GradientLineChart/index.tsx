/**
=========================================================
* Material Dashboard 2  React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

import React, { useRef, useEffect, useState, useMemo } from 'react';

// react-chartjs-2 components
import { Line } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';

// Chart.js 统一配置
import '../../../../utils/chartConfig';

// @mui material components
import Card from '@mui/material/Card';
import Icon from '@mui/material/Icon';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

// Material Dashboard 2 React helper functions
import gradientChartLine from 'assets/theme/functions/gradientChartLine';

// GradientLineChart configurations
import configs from 'examples/Charts/LineCharts/GradientLineChart/configs';

// Material Dashboard 2 React base styles
import colors from 'assets/theme/base/colors';

// Helper function to get color from theme
const getColorValue = (colorKey: string): string => {
  const colorMap: Record<string, any> = colors;
  if (colorKey in colorMap && colorMap[colorKey]?.main) {
    return colorMap[colorKey].main;
  }
  return colors.dark.main;
};

interface GradientLineChartProps {
  icon?: {
    color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark';
    component?: React.ReactNode;
  };
  title?: string;
  description?: string | React.ReactNode;
  height?: string | number;
  chart: {
    labels?: string[];
    datasets?: Array<{
      label?: string;
      color?: string;
      data?: number[];
      [key: string]: any;
    }>;
  };
}

interface ChartState {
  data?: ChartData<'line'>;
  options?: ChartOptions<'line'>;
}

function GradientLineChart({
  icon = { color: 'info', component: '' },
  title = '',
  description = '',
  height = '19.125rem',
  chart
}: GradientLineChartProps) {
  const chartRef = useRef<any>(null);
  const [chartData, setChartData] = useState<ChartState>({});

  useEffect(() => {
    const chartElement = chartRef.current;

    if (!chartElement) {
      return;
    }

    const chartDatasets = chart.datasets
      ? chart.datasets.map(dataset => ({
          ...dataset,
          tension: 0,
          pointRadius: 0,
          borderWidth: 4,
          borderColor: dataset.color ? getColorValue(dataset.color) : colors.dark.main,
          fill: true,
          maxBarThickness: 6,
          backgroundColor: gradientChartLine(
            chartElement.ctx,
            dataset.color ? getColorValue(dataset.color) : colors.dark.main
          )
        }))
      : [];

    setChartData(configs(chart.labels || [], chartDatasets));
  }, [chart]);

  const { data, options } = useMemo(() => chartData, [chartData]);

  const renderChart = (
    <MDBox sx={{ py: 2, pr: 2, pl: icon.component ? 1 : 2 }}>
      {title || description ? (
        <MDBox sx={{ display: 'flex', px: description ? 1 : 0, pt: description ? 1 : 0 }}>
          {icon.component && (
            <MDBox
              bgColor={icon.color || 'dark'}
              variant="gradient"
              coloredShadow={icon.color || 'dark'}
              borderRadius="xl"
              color="white"
              sx={{
                width: '4rem',
                height: '4rem',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                mt: -5,
                mr: 2
              }}
            >
              <Icon fontSize="medium">{icon.component}</Icon>
            </MDBox>
          )}
          <MDBox sx={{ mt: icon.component ? -2 : 0 }}>
            {title && <MDTypography variant="h6">{title}</MDTypography>}
            <MDBox sx={{ mb: 2 }}>
              <MDTypography component="div" variant="button" color="text">
                {description}
              </MDTypography>
            </MDBox>
          </MDBox>
        </MDBox>
      ) : null}
      <MDBox sx={{ height }}>
        <Line
          ref={chartRef}
          data={{
            labels: data?.labels || [],
            datasets: data?.datasets || []
          }}
          options={options}
          redraw
        />
      </MDBox>
    </MDBox>
  );

  return title || description ? <Card>{renderChart}</Card> : renderChart;
}

export default GradientLineChart;
