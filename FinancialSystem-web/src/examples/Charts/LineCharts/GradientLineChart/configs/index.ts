/**
=========================================================
* NextJS Material Dashboard 2 - v2.1.0
=========================================================

* Product Page: https://www.creative-tim.com/product/nextjs-material-dashboard-pro
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// NextJS Material Dashboard 2 base styles
import typography from 'assets/theme/base/typography';
import { ChartOptions, ChartData, InteractionMode } from 'chart.js';

interface ConfigsParams {
  labels: string[];
  datasets: any[];
}

interface ConfigsReturn {
  data: ChartData<'line'>;
  options: ChartOptions<'line'>;
}

function configs(labels: string[], datasets: any[]): ConfigsReturn {
  return {
    data: {
      labels,
      datasets: [...datasets]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      interaction: {
        intersect: false,
        mode: 'index' as InteractionMode
      },
      scales: {
        y: {
          grid: {
            display: true,
            drawOnChartArea: true,
            drawTicks: false
          },
          ticks: {
            display: true,
            padding: 10,
            color: '#b2b9bf',
            font: {
              size: 11,
              family: typography.fontFamily,
              style: 'normal',
              lineHeight: 2
            }
          }
        },
        x: {
          grid: {
            display: false,
            drawOnChartArea: false,
            drawTicks: false
          },
          ticks: {
            display: true,
            color: '#b2b9bf',
            padding: 20,
            font: {
              size: 11,
              family: typography.fontFamily,
              style: 'normal',
              lineHeight: 2
            }
          }
        }
      }
    }
  };
}

export default configs;
